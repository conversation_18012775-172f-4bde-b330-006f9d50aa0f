﻿CREATE VIEW [dbo].[TaxGroups]
AS 

SELECT Source.Id,
       Source.Code,
       (SELECT TOP 1 ExternalRecordId
          FROM dbo.Coupling
         WHERE Coupling.RecordId = Source.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS ExternalCode,
       Source.[Name],
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code AS ExternalSystemCode,
       Source.[ModifiedOn],
       t1.JSON

  FROM dbo.TaxGroup AS Source

 OUTER APPLY dbo.ExternalSystem

 OUTER APPLY (SELECT(
              SELECT TaxGroup.Id AS 'id',
                     ExternalSystem.Code AS 'externalSystemCode',
                     UPPER(TaxGroup.Code) AS 'code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = TaxGroup.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'externalCode',
                     TaxGroup.[Name] AS 'name',
                     TaxGroup.Rate AS 'rate'

                FROM dbo.TaxGroup

                WHERE TaxGroup.Id = Source.Id

                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                      ) AS JSON
                      ) AS t1