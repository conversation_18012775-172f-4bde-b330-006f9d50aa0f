{"dependencies": {"storage1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/Microsoft.Storage/storageAccounts/cosiumfruatfunctionapp", "type": "storage.azure", "connectionId": "AzureWebJobsStorage"}, "appInsights1": {"resourceId": "/subscriptions/[parameters('subscriptionId')]/resourceGroups/[parameters('resourceGroupName')]/providers/microsoft.insights/components/fr-uat", "type": "appInsights.azure", "connectionId": "APPLICATIONINSIGHTS_CONNECTION_STRING"}}}