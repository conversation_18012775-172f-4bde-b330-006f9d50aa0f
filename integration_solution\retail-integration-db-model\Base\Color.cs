﻿namespace WSA.Retail.Integration.Base.Color;

public class Color
{
    private string? externalSystemCode;
    private string? code;
    private string? externalCode;
    private string? name;
    private string? hexCode;

    public Guid Id { get; set; } = Guid.NewGuid();
    public string? Code
    {
        get { return code; }
        set
        {
            code = value;
            ValidateNullOrEmpty(code, nameof(Code));
            ValidateLength(code, 20, nameof(Code));
        }
    }

    public string? ExternalSystemCode
    {
        get { return externalSystemCode; }
        set
        {
            externalSystemCode = value;
            ValidateNullOrEmpty(externalSystemCode, nameof(ExternalSystemCode));
            ValidateLength(externalSystemCode, 20, nameof(ExternalSystemCode));
        }
    }

    public string? ExternalCode
    {
        get { return externalCode; }
        set
        {
            externalCode = value;
            ValidateNullOrEmpty(externalCode, nameof(ExternalCode));
            ValidateLength(externalCode, 100, nameof(ExternalCode));
        }
    }

    public string? Name
    {
        get { return name; }
        set
        {
            name = value;
            ValidateLength(name, 100, nameof(Name));
        }
    }

    public string? HexCode
    {
        get { return hexCode; }
        set
        {
            hexCode = value;
            ValidateLength(hexCode, 100, nameof(HexCode));
        }
    }

    public bool? IsActive { get; set; }


    private static void ValidateNullOrEmpty(string? value, string paramerterName)
    {
        if (value == null)
        {
            throw new ArgumentNullException(paramerterName);
        }
        else if (value == string.Empty)
        {
            throw new ArgumentException("Value cannot be empty.", paramerterName);
        }
    }

    private static void ValidateLength(string? value, int length, string paramerterName)
    {
        if (value is not null && value.Length > length)
        {
            throw new ArgumentOutOfRangeException(paramerterName, "Value cannot exceed " + length.ToString() + " characters.");
        }
    }
}

