﻿using Microsoft.EntityFrameworkCore;

namespace WSA.Retail.Integration.Cosium
{
    public partial class CosiumContext(DbContextOptions<CosiumContext> options) : DbContext(options)
    {
        public DbSet<Cosium.Clinics> Clinics { get; set; }
        public DbSet<Cosium.Patients> Patients { get; set; }
        public DbSet<Cosium.Client> Client { get; set; }
        public DbSet<Cosium.TauxTVA> TauxTVA { get; set; }
    }
}
