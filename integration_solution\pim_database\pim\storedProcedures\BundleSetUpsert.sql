﻿CREATE PROCEDURE [pim].[BundleSetUpsert]
(
    @source [pim].[BundleSetUpsertTableType] READONLY,

    -- Add output parameters for diagnostics
    @rowsReceived INT = NULL OUTPUT,
    @diagnosticMessage NVARCHAR(MAX) = NULL OUTPUT
)
AS
BEGIN
DECLARE @distinctBundleSetCount INT;
DECLARE @currentBundleSetCount INT;
DECLARE @currentProductBundleSetCount INT;
DECLARE @firstMergeAffected INT;
DECLARE @secondMergeAffected INT;
DECLARE @finalBundleSetCount INT;
DECLARE @finalProductBundleSetCount INT;
DECLARE @diagnosticLog NVARCHAR(MAX) = '';

-- Populate variables
SELECT @rowsReceived = COUNT(*) FROM @source;
SELECT @distinctBundleSetCount = COUNT(DISTINCT [BundleId]) FROM @source;
SELECT @currentBundleSetCount = COUNT(*) FROM pim.BundleSet;
SELECT @currentProductBundleSetCount = COUNT(*) FROM pim.ProductBundleSet;


-- Build diagnostic info
SET @diagnosticLog = 'Received ' + CAST(@rowsReceived AS VARCHAR(20)) + ' rows in @source parameter';
SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Distinct BundleSet Ids in @source: ' + CAST(@distinctBundleSetCount AS VARCHAR(20))
SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Current pim.BundleSet count: ' + CAST(@currentBundleSetCount AS VARCHAR(20))
SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Current pim.ProductBundleSet count: ' + CAST(@currentProductBundleSetCount AS VARCHAR(20));

 BEGIN TRY
   SET XACT_ABORT ON
 BEGIN TRANSACTION

 MERGE pim.BundleSet AS Target
 USING (SELECT t1.BundleId AS Id,
               t2.BundleName AS [Name],
               t2.BundleType AS [Type],
               t2.BundleIsDefault AS IsDefault,
               t2.BundleIsMandatory AS IsMandatory,
               t2.BundleIsMasterData AS IsMasterData,
               t2.BundleRanking AS Ranking,
               t2.BundleState AS [State],
               t2.BundleCreatedAt AS CreatedAt,
               t2.BundleUpdatedAt AS UpdatedAt
  FROM (SELECT DISTINCT BundleId FROM @source) AS t1
 OUTER APPLY (SELECT TOP 1
                     BundleName,
                     BundleType,
                     BundleIsDefault,
                     BundleIsMandatory,
                     BundleIsMasterData,
                     BundleRanking,
                     BundleState,
                     BundleCreatedAt,
                     BundleUpdatedAt
                FROM @source AS s2
               WHERE s2.BundleId = t1.BundleId
               ORDER BY s2.BundleUpdatedAt DESC) AS t2) AS Source

    ON Source.Id = Target.Id

  WHEN NOT MATCHED BY Target 
  THEN
INSERT (Id,
       [Name],
       [Type],
       IsDefault,
       IsMandatory,
       IsMasterData,
       Ranking,
       [State],
       CreatedAt,
       UpdatedAt)

VALUES (Source.Id,
       Source.[Name],
       Source.[Type],
       Source.IsDefault,
       Source.IsMandatory,
       Source.IsMasterData,
       Source.Ranking,
       Source.[State],
       Source.CreatedAt,
       Source.UpdatedAt)

  WHEN MATCHED AND 
       ((Target.[Name] <> Source.[Name]) OR (Target.[Name] IS NULL AND Source.[Name] IS NOT NULL)
    OR (Target.[Type] <> Source.[Type]) OR (Target.[Type] IS NULL AND Source.[Type] IS NOT NULL)
    OR (Target.IsDefault <> Source.IsDefault) OR (Target.IsDefault IS NULL AND Source.IsDefault IS NOT NULL)
    OR (Target.IsMandatory <> Source.IsMandatory) OR (Target.IsMandatory IS NULL AND Source.IsMandatory IS NOT NULL)
    OR (Target.IsMasterData <> Source.IsMasterData) OR (Target.IsMasterData IS NULL AND Source.IsMasterData IS NOT NULL)
    OR (Target.Ranking <> Source.Ranking) OR (Target.Ranking IS NULL AND Source.Ranking IS NOT NULL)
    OR (Target.[State] <> Source.[State]) OR (Target.[State] IS NULL AND Source.[State] IS NOT NULL)
    OR (Target.CreatedAt <> Source.CreatedAt) OR (Target.CreatedAt IS NULL AND Source.CreatedAt IS NOT NULL)
    OR (Target.UpdatedAt <> Source.UpdatedAt) OR (Target.UpdatedAt IS NULL AND Source.UpdatedAt IS NOT NULL))
         
  THEN 
UPDATE
   SET [Name] = Source.[Name],
       [Type] = Source.[Type],
       IsDefault = Source.IsDefault,
       IsMandatory = Source.IsMandatory,
       IsMasterData = Source.IsMasterData,
       Ranking = Source.Ranking,
       [State] = Source.[State],
       CreatedAt = Source.CreatedAt,
       UpdatedAt = Source.UpdatedAt,
       SystemModifiedOn = sysutcdatetime();

   SET @firstMergeAffected = @@rowCount;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Rows affected by pim.BundleSet MERGE: ' + CAST(@firstMergeAffected AS VARCHAR(20));


 MERGE pim.ProductBundleSet AS Target
 USING (SELECT t1.ProductId AS ProductId,
               t1.BundleId AS BundleSetId,
               t2.ProductIsAvailable AS IsAvailable,
               t2.ProductIsPhasedOut AS IsPhasedOut,
               t2.ProductIsDefault AS IsDefault,
               t2.ProductRanking AS Ranking,
               t2.ProductSku AS Sku,
               t2.ProductState AS [State],
               t2.ProductCreatedAt AS CreatedAt,
               t2.ProductUpdatedAt AS UpdatedAt
          FROM (SELECT DISTINCT ProductId, BundleId FROM @source) AS t1
         OUTER APPLY (SELECT TOP 1
                             ProductIsAvailable,
                             ProductIsPhasedOut,
                             ProductIsDefault,
                             ProductRanking,
                             ProductSku,
                             ProductState,
                             ProductCreatedAt,
                             ProductUpdatedAt
                        FROM @source AS s2
                       WHERE s2.ProductId = t1.ProductId AND s2.BundleId = t1.BundleId
                       ORDER BY s2.ProductUpdatedAt DESC) AS t2) AS Source
            ON Source.ProductId = Target.ProductId
           AND Source.BundleSetId = Target.BundleSetId

  WHEN NOT MATCHED BY Target 
  THEN
INSERT (ProductId, 
       BundleSetId,
       IsAvailable,
       IsPhasedOut,
       IsDefault,
       Ranking,
       Sku,
       [State],
       CreatedAt,
       UpdatedAt)

VALUES (Source.ProductId, 
       Source.BundleSetId,
       Source.IsAvailable,
       Source.IsPhasedOut,
       Source.IsDefault,
       Source.Ranking,
       Source.Sku,
       Source.[State],
       Source.CreatedAt,
       Source.UpdatedAt)
                      
  WHEN MATCHED AND (
       (Target.IsAvailable <> Source.IsAvailable) OR (Target.IsAvailable IS NULL AND Source.IsAvailable IS NOT NULL)
    OR (Target.IsPhasedOut <> Source.IsPhasedOut) OR (Target.IsPhasedOut IS NULL AND Source.IsPhasedOut IS NOT NULL)
    OR (Target.IsDefault <> Source.IsDefault) OR (Target.IsDefault IS NULL AND Source.IsDefault IS NOT NULL)
    OR (Target.Ranking <> Source.Ranking) OR (Target.Ranking IS NULL AND Source.Ranking IS NOT NULL)
    OR (Target.Sku <> Source.Sku) OR (Target.Sku IS NULL AND Source.Sku IS NOT NULL)
    OR (Target.[State] <> Source.[State]) OR (Target.[State] IS NULL AND Source.[State] IS NOT NULL)
    OR (Target.CreatedAt <> Source.CreatedAt) OR (Target.CreatedAt IS NULL AND Source.CreatedAt IS NOT NULL)
    OR (Target.UpdatedAt <> Source.UpdatedAt) OR (Target.UpdatedAt IS NULL AND Source.UpdatedAt IS NOT NULL))
                       
  THEN 
UPDATE
   SET IsAvailable = Source.IsAvailable,
       IsPhasedOut = Source.IsPhasedOut,
       IsDefault = Source.IsDefault,
       Ranking = Source.Ranking,
       Sku = Source.Sku,
       [State] = Source.[State],
       CreatedAt = Source.CreatedAt,
       UpdatedAt = Source.UpdatedAt,
       SystemModifiedOn = sysutcdatetime();

   SET @secondMergeAffected = @@rowCount;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Rows affected by pim.ProductBundleSet MERGE: ' + CAST(@secondMergeAffected AS VARCHAR(20));


COMMIT TRANSACTION;

-- Final counts
SELECT @finalBundleSetCount = COUNT(*) FROM pim.BundleSet;
SELECT @finalProductBundleSetCount = COUNT(*) FROM pim.ProductBundleSet;
SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Final pim.BundleSet count: ' + CAST(@finalBundleSetCount AS VARCHAR(20))
SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Final pim.ProductBundleSet count: ' + CAST(@finalProductBundleSetCount AS VARCHAR(20));

  
   END TRY
 BEGIN CATCH
    IF @@TRANCOUNT > 0
       ROLLBACK TRANSACTION;
            
       SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Error occurred: ' + ERROR_MESSAGE();
       SET @diagnosticMessage = @diagnosticLog;
       THROW;
   END CATCH
    
   SET @diagnosticMessage = @diagnosticLog;
END