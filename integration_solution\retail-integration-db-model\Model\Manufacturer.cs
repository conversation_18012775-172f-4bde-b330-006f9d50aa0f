﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model;

public class Manufacturer
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("externalSystemCode")]
    public string? ExternalSystemCode { get; set; }

    [JsonPropertyName("code")]
    public string? Code { get; set; }

    [JsonPropertyName("externalCode")]
    public string? ExternalCode { get; set; }

    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonPropertyName("address")]
    public string? Address { get; set; } = string.Empty;

    [JsonPropertyName("address2")]
    public string? Address2 { get; set; } = string.Empty;

    [JsonPropertyName("city")]
    public string? City { get; set; } = string.Empty;

    [JsonPropertyName("region")]
    public string? Region { get; set; } = string.Empty;

    [JsonPropertyName("country")]
    public string? Country { get; set; } = string.Empty;

    [JsonPropertyName("postalCode")]
    public string? PostalCode { get; set; } = string.Empty;

    [JsonPropertyName("phone")]
    public string? Phone { get; set; } = string.Empty;

    [JsonPropertyName("email")]
    public string? Email { get; set; } = string.Empty;


    public Manufacturer() { }
}