# {{className}} Adapter

## 📋 Overview

**Namespace**: `{{namespace}}`  
**Source File**: `{{filePath}}`  
**Purpose**: {{purpose}}

{{description}}

## 🔄 Implemented Interfaces

{{#each interfaces}}
- `{{name}}` - {{description}}
{{/each}}

## 🛠️ Methods

{{#each methods}}
### {{name}}

**Signature**: `{{signature}}`  
**Purpose**: {{purpose}}

#### Parameters
{{#each parameters}}
- **{{name}}** (`{{type}}`): {{description}}
{{/each}}

#### Returns
- **Type**: `{{returnType}}`
- **Description**: {{returnDescription}}

#### Implementation
```csharp
{{implementation}}
```

#### Transformation Logic
{{#each transformations}}
- **{{sourceProperty}}** → **{{targetProperty}}**: {{logic}}
  {{#if validation}}- Validation: {{validation}}{{/if}}
  {{#if defaultValue}}- Default: {{defaultValue}}{{/if}}
{{/each}}

#### Business Rules
{{#each businessRules}}
- {{rule}}
{{/each}}

#### Validation Rules
{{#each validationRules}}
- {{rule}}
  {{#if errorCondition}}- Error Condition: {{errorCondition}}{{/if}}
  {{#if errorMessage}}- Error Message: "{{errorMessage}}"{{/if}}
{{/each}}

---
{{/each}}

## 🔀 Data Mapping

### Property Mappings

| Source Property | Target Property | Transformation | Validation | Notes |
|----------------|-----------------|----------------|------------|-------|
{{#each propertyMappings}}
| `{{source}}` | `{{target}}` | {{transformation}} | {{validation}} | {{notes}} |
{{/each}}

### Complex Transformations

{{#each complexTransformations}}
#### {{name}}
**Description**: {{description}}

**Logic**:
```csharp
{{logic}}
```

**Validation**:
{{#each validations}}
- {{validation}}
{{/each}}

**Error Handling**:
{{#each errorHandling}}
- {{error}}
{{/each}}
{{/each}}

## 🎯 Integration Patterns

### Event Hub Integration
{{#if eventHubIntegration}}
- **Event Type**: `{{eventType}}`
- **External System Code**: {{externalSystemCode}}
- **Event Processing**: {{eventProcessing}}

#### Event Transformation
```csharp
{{eventTransformation}}
```
{{/if}}

### Manage API Integration
{{#if manageApiIntegration}}
- **Request Type**: `{{requestType}}`
- **Response Type**: `{{responseType}}`
- **API Endpoint**: {{apiEndpoint}}

#### Request Transformation
```csharp
{{requestTransformation}}
```

#### Response Transformation
```csharp
{{responseTransformation}}
```
{{/if}}

### Business Central Integration
{{#if bcIntegration}}
- **BC Entity Type**: `{{bcEntityType}}`
- **API Version**: {{apiVersion}}
- **Data Contract**: {{dataContract}}

#### BC to Domain Transformation
```csharp
{{bcToDomainTransformation}}
```

#### Domain to BC Transformation
```csharp
{{domainToBcTransformation}}
```
{{/if}}

## ⚠️ Error Handling

### Exception Types
{{#each exceptionTypes}}
- **{{type}}**: {{description}}
  - **Trigger**: {{trigger}}
  - **Resolution**: {{resolution}}
{{/each}}

### Validation Failures
{{#each validationFailures}}
- **{{field}}**: {{error}}
  - **Condition**: {{condition}}
  - **Action**: {{action}}
{{/each}}

## 🧪 Usage Examples

### Basic Usage
```csharp
{{basicUsageExample}}
```

### With Validation
```csharp
{{validationExample}}
```

### Error Handling
```csharp
{{errorHandlingExample}}
```

## 🔗 Dependencies

### Required Services
{{#each requiredServices}}
- **{{name}}** (`{{interface}}`): {{purpose}}
{{/each}}

### External References
{{#each externalReferences}}
- **{{name}}**: {{description}}
  - **Type**: {{type}}
  - **Usage**: {{usage}}
{{/each}}

## 📊 Performance Considerations

{{#each performanceNotes}}
- {{note}}
{{/each}}

## 🔗 Related Components

{{#each relatedComponents}}
- [{{name}}]({{link}}) - {{relationship}}
{{/each}}

## 📝 Notes

{{#each notes}}
- {{note}}
{{/each}}

---

*Generated on {{generatedDate}} from source code*  
*Last modified: {{lastModified}}*
