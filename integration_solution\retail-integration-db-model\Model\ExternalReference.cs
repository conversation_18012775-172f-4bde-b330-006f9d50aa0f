﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model
{
    public class ExternalReference
    {
        [JsonPropertyName("id")]
        public Guid? Id { get; set; }
        [JsonPropertyName("code")]
        public string? Code { get; set; }
        [JsonPropertyName("externalCode")]
        public string? ExternalCode { get; set; }
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        public ExternalReference() { }
    }
}
