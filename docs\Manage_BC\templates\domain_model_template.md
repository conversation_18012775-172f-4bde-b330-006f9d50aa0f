# {{className}} Domain Model

## Overview
**File**: `{{filePath}}`  
**Namespace**: `{{namespace}}`

## Class Definition
```csharp
{{classDefinition}}
```

## Properties
| Property | Type | Required | Constraints | Description |
|----------|------|----------|-------------|-------------|
{{#each properties}}
| `{{name}}` | {{type}} | {{required}} | {{constraints}} | {{description}} |
{{/each}}

## Interfaces
{{#each interfaces}}
- `{{name}}` - {{description}}
{{/each}}

## JSON Serialization
{{#each jsonProperties}}
### {{propertyName}}
- **JSON Name**: `{{jsonName}}`
- **Type**: `{{type}}`
{{/each}}

## Validation Rules
{{#each validationRules}}
- **{{property}}**: {{rule}}
{{/each}}

## Business Rules
{{#each businessRules}}
- {{rule}}
{{/each}}

## Usage Examples
```csharp
{{usageExample}}
```

## Related Entities
{{#each relatedEntities}}
- [{{name}}]({{link}}) - {{relationship}}
{{/each}}

---
*Generated: {{generatedDate}}*
