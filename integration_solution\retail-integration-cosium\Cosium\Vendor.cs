﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using WSA.Retail.Integration.Model;

namespace WSA.Retail.Integration.Cosium
{
    public class Vendor : Model.Vendor
    {
        public Vendor() { }

        public Vendor(string requestBody) : base(requestBody) { }

        private static HttpClient? _client;

        public static async Task ProcessNewRecordsAsync(ILogger log)
        {
            var scope = InitScope(nameof(ProcessNewRecordsAsync));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(ProcessNewRecordsAsync));

            List<Vendor?> vendors = await GetFromSource(log);
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] Fetched {recordCount} vendors from Cosium database",
                ClassName(), nameof(ProcessNewRecordsAsync), vendors?.Count);

            if (vendors?.Count > 0)
            {
                await PostListAsync(vendors, log);
            }
            else
            {
                log.BeginScope(scope);
                log.LogInformation("[{className}].[{procedureName}] No new vendors are avalable in the Cosium database.", 
                    ClassName(), nameof(ProcessNewRecordsAsync));
            }
        }

        private static async Task<List<Vendor?>> GetFromSource(ILogger log)
        {
            var scope = InitScope(nameof(GetFromSource));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromSource));

            List<Vendor?> vendorList = [];

            using SqlConnection conn = new(Common.SqlConnectionString());
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "SELECT * FROM cosium.Vendors WHERE IntegrationRequired = 1";
                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        {
                            if (reader.HasRows)
                            {
                                DataTable dt = new();
                                dt.Load(reader);
                                log.LogInformation("[{className}].[{procedureName}] SQL reader returned {rowCount} rows.", 
                                    ClassName(), nameof(GetFromSource), dt.Rows.Count);

                                foreach (DataRow dr in dt.Rows)
                                {
                                    Vendor newVendor = new((string)dr["JSON"]);
                                    vendorList.Add(newVendor);
                                }
                                return vendorList;
                            }
                            else
                            {
                                log.LogInformation("[{className}].[{procedureName}] No new vendors were retrieved from the database.",
                                    ClassName(), nameof(GetFromSource));
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.BeginScope(scope);
                    log.LogError(ex, "[{className}].[{procedureName}] Attempt to fetch vendors from the database generated an exception:\r\n{object}",
                        ClassName(), nameof(GetFromSource), ex.Message);
                }
            }
            return vendorList;
        }

        public static async Task<Vendor?> GetFromBaseAsync(ILogger log, string? code = null, string? externalCode = null)
        {
            var scope = InitScope(nameof(GetFromBaseAsync));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromBaseAsync));

            Vendor? vendor = new();
            if (await vendor.GetAsync(scope, log, Common.SqlConnectionString(), Common.ExternalSystemCode(), code: code, externalCode: externalCode))
            {
                if (vendor.Id != null)
                {
                    return vendor;
                }
            }
            return null;
        }

        public static async Task<Vendor?> GetFromDatabaseById(int thisId, ILogger log)
        {
            var scope = InitScope(nameof(GetFromDatabaseById));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromDatabaseById));

            using SqlConnection conn = new(Common.SqlConnectionString());
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "SELECT * FROM cosium.Vendors WHERE ExternalCode = @thisId";
                        cmd.Parameters.AddWithValue("@thisId", thisId);

                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        {
                            if (reader.HasRows)
                            {
                                DataTable dt = new();
                                dt.Load(reader);
                                log.LogInformation("[{className}].[{procedureName}] SQL reader returned {rowCount} rows", 
                                    ClassName(), nameof(GetFromDatabaseById), dt.Rows.Count);

                                DataRow dr = dt.Rows[0];
                                Vendor? newRecord = new((string)dr["JSON"]);

                                return newRecord;
                            }
                            else
                            {
                                log.LogInformation("[{className}].[{procedureName}] No new vendors were retrieved from the database.", 
                                    ClassName(), nameof(GetFromDatabaseById));
                            }
                        }
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "[{className}].[{procedureName}] Attempt to fetch vendors from the database generated an exception:\r\n{object}",
                        ClassName(), nameof(GetFromDatabaseById), ex.Message);
                }
            }
            return null;
        }

        public static async Task PostListAsync(List<Vendor?> vendors, ILogger log)
        {
            var scope = InitScope(nameof(PostListAsync));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(PostListAsync));

            var entitySubscriber = await EntitySubscriber.GetAsync(null, Common.ExternalSystemCode(), "VENDOR");
            ArgumentNullException.ThrowIfNull(entitySubscriber);
            if (entitySubscriber.FromExternalSystem ?? false == true)
            {
                try
                {
                    foreach (var vendor in vendors)
                    {
                        if (vendor != null)
                        {
                            await vendor.PostAsync(log, entitySubscriber);
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.BeginScope(scope);
                    log.LogError(ex, "[{className}].[{procedureName}] Encountered an exception:\r\n{object}",
                        ClassName(), nameof(PostListAsync), ex.Message);
                }
            }
        }

        public async Task<Vendor?> PostAsync(ILogger log, Model.EntitySubscriber? entitySubscriber = null)
        {
            var scope = InitScope(nameof(PostAsync));
            AddEntityToScope(ref scope, this);
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(PostAsync));

            if (entitySubscriber == null)
            {
                entitySubscriber = await EntitySubscriber.GetAsync(null, Common.ExternalSystemCode(), "CLINIC");
                ArgumentNullException.ThrowIfNull(entitySubscriber);
            }
            if (entitySubscriber.FromExternalSystem ?? false == true)
            {
                _client ??= Common.GetHttpClient();

                try
                {
                    if (this != null)
                    {
                        AddEntityToScope(ref scope, this);
                        var requestBody = JsonSerializer.Serialize(this, Common.GetJsonOptions());
                        var content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                        var request = new HttpRequestMessage(HttpMethod.Post, "vendors")
                        {
                            Content = content
                        };

                        log.LogInformation("[{className}].[{procedureName}] Sending vendor: {code} to API\r\n{requestBody}",
                            ClassName(), nameof(PostAsync), this.Code, requestBody);
                        HttpResponseMessage response = await _client.SendAsync(request);
                        var responseBody = await response.Content.ReadAsStringAsync();
                        responseBody = JsonObject.Parse(responseBody)?.ToJsonString(Common.GetJsonOptions()) ?? "";
                        log.LogInformation("[{className}].[{procedureName}] API response:\r\n{status}\r\nBody:\r\n{responseBody}",
                            ClassName(), nameof(PostAsync), response.StatusCode.ToString() + " - " + response.RequestMessage, responseBody);

                        if (response.StatusCode == System.Net.HttpStatusCode.OK)
                        {
                            log.LogInformation("[{className}].[{procedureName}] Successfully updated vendor: {code}",
                                ClassName(), nameof(PostAsync), this.Code);
                            var vendor = new Vendor(responseBody);
                            if (vendor != null)
                            {
                                UpdateIsIntegrated();
                                return vendor;
                            }
                        }
                        else
                        {
                            log.LogError("Attempt to update vendor: {code} failed\r\nMessage:{responseBody}",
                                this.Code, responseBody);
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.BeginScope(scope);
                    log.LogError(ex, "[{className}].[{procedureName}] Encountered an exception:\r\n{object}",
                        ClassName(), nameof(PostAsync), ex.Message);
                }
            }
            return null;
        }

        private bool UpdateIsIntegrated()
        {
            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);
            ArgumentNullException.ThrowIfNull(this.ExternalCode);

            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "UPDATE cosium.Vendors SET IntegrationRequired = 0, IntegrationDate = sysutcdatetime() WHERE ExternalCode = @id";
                        cmd.Parameters.AddWithValue("@id", this.ExternalCode);
                        cmd.ExecuteNonQuery();
                        conn.Close();
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            }
        }

        public static async Task ValidateExternalReference(Model.ExternalReference? externalReference, ILogger log)
        {
            var scope = InitScope(nameof(ValidateExternalReference));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(ValidateExternalReference));

            if (externalReference != null && externalReference.ExternalCode != null)
            {
                ArgumentNullException.ThrowIfNull(externalReference.ExternalCode);
                Vendor? vendor = await GetFromBaseAsync(log, externalCode: externalReference.ExternalCode);
                {
                    if (vendor == null)
                    {
                        vendor = await GetFromDatabaseById(int.Parse(externalReference.ExternalCode), log);
                        if (vendor != null)
                        {
                            vendor = await vendor.PostAsync(log);
                        }
                    }
                }

                ArgumentNullException.ThrowIfNull(vendor);
                ArgumentNullException.ThrowIfNull(vendor.Id);
                externalReference.Id = vendor.Id;
                externalReference.ExternalCode = vendor.ExternalCode;
                externalReference.Code = vendor.Code;
                externalReference.Name = vendor.Name;
            }
        }

        private static string ClassName()
        {
            return nameof(Clinic);
        }

        private static Dictionary<string, object> InitScope(string procedureName)
        {
            return new Dictionary<string, object>() {
                { "appName", "Cosium" },
                { "className", nameof(Vendor) },
                { "procedureName", procedureName }
            };
        }

        private static void AddEntityToScope(ref Dictionary<string, object> scope, Vendor vendor)
        {
            scope["entityName"] = "Vendor";
            scope["entityCode"] = vendor.Code ?? "";
        }
    }
}