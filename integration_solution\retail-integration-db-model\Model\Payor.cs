﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model;

public class Payor
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }
    [JsonPropertyName("externalSystemCode")]
    public string? ExternalSystemCode { get; set; }
    [JsonPropertyName("code")]
    public string? Code { get; set; }
    [JsonPropertyName("externalCode")]
    public string? ExternalCode { get; set; }
    [JsonPropertyName("name")]
    public string? Name { get; set; }
    [JsonPropertyName("address")]
    public string Address { get; set; } = "";
    [JsonPropertyName("address2")]
    public string Address2 { get; set; } = "";
    [JsonPropertyName("city")]
    public string City { get; set; } = "";
    [JsonPropertyName("region")]
    public string Region { get; set; } = "";
    [JsonPropertyName("country")]
    public string Country { get; set; } = "";
    [<PERSON>sonPropertyName("postalCode")]
    public string PostalCode { get; set; } = "";
    [JsonPropertyName("phone")]
    public string Phone { get; set; } = "";
    [JsonPropertyName("email")]
    public string Email { get; set; } = "";
    [JsonPropertyName("accountNo")]
    public string AccountNo { get; set; } = "";


    public Payor() { }
}