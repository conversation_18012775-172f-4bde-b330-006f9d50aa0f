using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using WSA.Retail.Integration.Cosium;
using WSA.Retail.Integration.DbModel;

var builder = FunctionsApplication.CreateBuilder(args);
builder.ConfigureFunctionsWebApplication();
builder.Services
    .AddApplicationInsightsTelemetryWorkerService()
    .ConfigureFunctionsApplicationInsights();

string? connectionString = Environment.GetEnvironmentVariable("SqlConnectionString");
ArgumentNullException.ThrowIfNull(connectionString, nameof(connectionString));
builder.Services.AddDbContext<CosiumContext>(
    options => SqlServerDbContextOptionsExtensions.UseSqlServer(options, connectionString)
                                                  .LogTo(s => System.Diagnostics.Debug.WriteLine(s))
                                                  .EnableDetailedErrors(false)
                                                  .EnableSensitiveDataLogging(true));
builder.Services.AddDbContext<IntegrationContext>(
options => SqlServerDbContextOptionsExtensions.UseSqlServer(options, connectionString)
                                              .LogTo(s => System.Diagnostics.Debug.WriteLine(s))
                                              .EnableDetailedErrors(false)
                                              .EnableSensitiveDataLogging(true));

builder.Build().Run();