CREATE TABLE [dbo].[Battery]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_Battery_Id] DEFAULT NEWID() NOT NULL,
    [Code]              NVARCHAR(20) NOT NULL,
    [Name]              NVARCHAR(100) NULL,
    [AlternateCode]     NVARCHAR(50) NULL,
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_Battery_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_Battery_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_Battery_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_Battery_Code
ON [dbo].[Battery] ([Code])
GO

CREATE INDEX IX_Battery_Name
ON [dbo].[Battery] ([Name])
GO