﻿CREATE TABLE [pim].[CountryBrand]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_CountryBrand_Id] DEFAULT NEWID() NOT NULL,
    [CountryId]             UNIQUEIDENTIFIER NULL,
    [BrandId]               UNIQUEIDENTIFIER NULL,
    [IsActive]              BIT CONSTRAINT [DF_CountryBrand_IsActive] DEFAULT 1 NOT NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_CountryBrand_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_CountryBrand_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_CountryBrand_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_CountryBrand_CountryId_BrandId
ON [pim].[CountryBrand] ([CountryId], [BrandId])
GO
