using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using System.IO.Compression;

namespace WSA.Retail.Integration.Cosium
{
    public class Utilities(ILogger<Utilities> logger)
    {
        private readonly ILogger<Utilities> _logger = logger;
        private BlobServiceClient? _blobServiceClient;

        [Function("HandleFlatFile")]

        public IActionResult HandleFlatFile([HttpTrigger(AuthorizationLevel.Function, "post")] HttpRequest req)
        {
            string? containerName = Common.GetQueryValue(req.Query, "containerName");
            string? destination = Common.GetQueryValue(req.Query, "destination");
            string? path = Common.GetQueryValue(req.Query, "path");

            ArgumentNullException.ThrowIfNull(containerName);
            ArgumentNullException.ThrowIfNull(destination);
            ArgumentNullException.ThrowIfNull(path);

            BlobClient? oldBlob = null;
            string? fileName = null;
            string? fileContent = null;
            if (GetFileContents(ref oldBlob, containerName, path, ref fileName, ref fileContent))
            {
                if (fileContent !=  null)
                {
                    fileContent = fileContent.Replace("\r", "");
                    string newPath = destination.TrimEnd('/') + '/' + fileName;
                    _logger.LogInformation("Replaced CR in file: {path}", fileName);
                    if (UploadFile(ref fileContent, containerName, newPath))
                    {
                        _logger.LogInformation("Uploaded file: {path}", newPath);
                        oldBlob?.Delete();
                        _logger.LogInformation("Deleted  old file: {path}", newPath);
                        return new OkObjectResult("Welcome to Azure Functions!");
                    }
                }
            }

            return new BadRequestResult();
        }


        private void Init()
        {
            var connectionString = Environment.GetEnvironmentVariable("StorageAccountConnectionString");
            ArgumentNullException.ThrowIfNull(connectionString);
            _blobServiceClient = new BlobServiceClient(connectionString);
        }

        private bool GetFileContents(
            ref BlobClient? blob, 
            string containerName, 
            string path,
            ref string? fileName,
            ref string? fileContents)
        {
            if (_blobServiceClient == null)
            {
                Init();
            }

            _logger.LogInformation("Getting contents of file: {path}", path);
            blob = _blobServiceClient?.GetBlobContainerClient(containerName).GetBlobClient(path);
            using var stream = new MemoryStream();
            try
            {
                blob?.DownloadTo(stream);
                _logger.LogInformation("Successfully downloaded file: {path}", path);
                using var zip = new ZipArchive(stream);
                foreach (var item in zip.Entries)
                {
                    fileName = item.FullName;
                    using var csvStream = item.Open();
                    using var reader = new StreamReader(csvStream);
                    fileContents = reader.ReadToEnd();
                    _logger.LogInformation("Unzipped archive file: {path}", path);
                }
            }
            catch
            {
                return false;
            }
            return true;
        }


        private bool UploadFile(ref string fileContents, string containerName, string path)
        {
            BlobClient? blob = _blobServiceClient?.GetBlobContainerClient(containerName).GetBlobClient(path);
            try
            {
                blob?.Upload(BinaryData.FromString(fileContents), overwrite: true);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
