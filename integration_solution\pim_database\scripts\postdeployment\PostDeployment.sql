﻿/*
Post-Deployment Script Template                                                 
--------------------------------------------------------------------------------------
 This file contains SQL statements that will be appended to the build script.              
 Use SQLCMD syntax to include a file in the post-deployment script.                     
 Example:      :r .\myfile.sql                                                        
 Use SQLCMD syntax to reference a variable in the post-deployment script.              
 Example:      :setvar TableName MyTable                                                 
               SELECT * FROM [$(TableName)]                                   
--------------------------------------------------------------------------------------
*/
:r .\scripts\ExternalSystems.sql
:r .\scripts\EntitySubscribers.sql

:r .\scripts\Categories.sql

:r .\scripts\Brands.sql

:r .\scripts\Manufacturers.sql

:r .\scripts\Batteries.sql

:r .\scripts\User.sql