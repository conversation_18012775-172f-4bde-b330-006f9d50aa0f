﻿CREATE VIEW [dbo].[SalesInvoices]
AS

SELECT Source.Id,
       Source.DocumentNumber,
       (SELECT TOP 1 ExternalRecordId
          FROM dbo.Coupling
         WHERE Coupling.RecordId = Source.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS ExternalReference,
       Source.DocumentDate,
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code AS ExternalSystemCode,
       Source.ModifiedOn,
       t1.JSON

  FROM dbo.SalesInvoice AS Source

 OUTER APPLY dbo.ExternalSystem

 OUTER APPLY (SELECT(
              SELECT SalesInvoice.Id AS 'id',
                     SalesInvoice.DocumentNumber AS 'documentNumber',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = SalesInvoice.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'externalReference',
                     SalesInvoice.AlternateNumber AS 'alternateNumber',

                     SalesOrder.Id AS 'salesOrder.id',
                     SalesOrder.DocumentNumber AS 'salesOrder.documentNumber',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = SalesOrder.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'salesOrder.externalReference',
                     
                     Patient.Id AS 'patient.id',
                     Patient.Code AS 'patient.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Patient.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'patient.externalCode',
                     Patient.[Name] AS 'patient.name',

                     Clinic.Id AS 'clinic.id',
                     Clinic.Code AS 'clinic.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Patient.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'clinic.externalCode',
                     Clinic.[Name] AS 'clinic.name',
                     SalesInvoice.DocumentDate AS documentDate,
                     (
                            SELECT SalesInvoiceLine.[Sequence] AS [sequence],
                                   Product.Id AS 'product.id',
                                   Product.Code AS 'product.code',
                                   (SELECT TOP 1 ExternalRecordId 
                                      FROM dbo.Coupling 
                                      WHERE Coupling.RecordId = Patient.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'product.externalCode',
                                   Product.[Name] AS 'product.name',

                                   SalesInvoiceLine.[Description] AS [description],
                                   SalesInvoiceLine.[Quantity] AS quantity,
                                   SalesInvoiceLine.[UnitPrice] AS unitPrice,
                                   SalesInvoiceLine.[GrossAmount] AS grossAmount,
                                   SalesInvoiceLine.[DiscountAmount] AS discountAmount,
                                   SalesInvoiceLine.[AmountInclTax] AS amountInclTax,
                                   SalesInvoiceLine.[TaxAmount] AS taxAmount,
                                   SalesInvoiceLine.[AmountExclTax] AS amountExclTax,
                                   SalesInvoiceLine.[SerialNumber] AS serialNumber

                              FROM dbo.SalesInvoiceLine

                              LEFT JOIN dbo.Product
                                ON SalesInvoiceLine.ProductId = Product.Id

                             WHERE SalesInvoiceLine.SalesInvoiceId = SalesInvoice.Id
                               AND SalesInvoiceLine.IsActive = 1

                             ORDER BY SalesInvoiceLine.[Sequence]

                               FOR JSON PATH
                     ) AS salesInvoiceLines

                FROM dbo.SalesInvoice

                LEFT JOIN dbo.Patient
                  ON SalesInvoice.PatientId = Patient.Id

                LEFT JOIN dbo.Clinic
                  ON SalesInvoice.ClinicId = Clinic.Id

                LEFT JOIN dbo.SalesOrder
                       ON SalesInvoice.SalesOrderId = SalesOrder.Id

               WHERE SalesInvoice.Id = Source.Id

                FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                  ) AS JSON
                  ) AS t1

 WHERE Source.IsActive = 1