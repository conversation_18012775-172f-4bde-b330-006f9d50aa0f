﻿CREATE VIEW [dbo].[Products]
AS

  WITH Source AS (

SELECT Product.Id,
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code AS ExternalSystemCode,
       Product.Code,
       pcoup.RecordId AS ExternalCode,
       Product.[Name],
       Product.AlternateCode,
       Product.PimProductId,
       Product.ProductCategoryId AS CategoryId,
       ProductCategory.Code AS CategoryCode,
       ProductCategory.Name AS CategoryName,
       Product.VendorId,
       Vendor.Code AS VendorCode,
       Vendor.Name AS VendorName,
       Product.ManufacturerId,
       Manufacturer.Code AS ManufacturerCode,
       Manufacturer.Name AS ManufacturerName,
       Product.ColorId,
       Color.Code AS ColorCode,
       Color.Name AS ColorName,
       Product.TaxGroupId,
       TaxGroup.Code AS TaxGroupCode,
       TaxGroup.Name AS TaxGroupName,
       Product.VendorItemNo,
       Product.GTIN,
       Product.ImageUrl,
       Product.IsSerialized,
       Product.IsInventory,
       Product.IsTaxable,
       Product.UnitCost,
       Product.ProductModelId,
       ProductModel.Code AS ProductModelCode,
       ProductModel.Name AS ProductModelName,
       Product.BatteryId,
       Battery.Code AS BatteryCode,
       Battery.Name AS BatteryName,
       Product.CreatedOn,
       Product.ModifiedOn

  FROM dbo.Product

 OUTER APPLY dbo.ExternalSystem

  LEFT JOIN dbo.Coupling AS pcoup
    ON Product.Id = pcoup.RecordId
   AND ExternalSystem.Id = pcoup.ExternalSystemId

  LEFT JOIN dbo.ProductCategory
    ON Product.ProductCategoryId = ProductCategory.Id

 LEFT JOIN dbo.Vendor
   ON Product.VendorId = Vendor.Id

 LEFT JOIN dbo.Manufacturer
   ON Product.ManufacturerId = Manufacturer.Id

 LEFT JOIN dbo.Color
   ON Product.ColorId = Color.Id

 LEFT JOIN dbo.TaxGroup
   ON Product.TaxGroupId = TaxGroup.Id

 LEFT JOIN dbo.ProductModel
   ON Product.ProductModelId = ProductModel.Id

 LEFT JOIN dbo.Battery
   ON Product.BatteryId = Battery.Id

)

SELECT Source.Id,
       Source.ExternalSystemCode AS ExternalSystem,
       Source.Code,
       Source.ExternalCode,
       Source.Name,
       Source.AlternateCode,
       Source.PimProductId,
       Source.CategoryCode AS Category,
       Source.VendorCode AS Vendor,
       Source.ManufacturerCode AS Manufacturer,
       Source.ColorCode AS Color,
       Source.TaxGroupCode AS TaxGroup,
       Source.VendorItemNo,
       Source.GTIN,
       Source.ImageUrl,
       Source.IsSerialized,
       Source.IsInventory,
       Source.IsTaxable,
       Source.UnitCost,
       Source.ProductModelCode AS ProductModel,
       Source.BatteryCode AS Battery,
       Source.CreatedOn,
       Source.ModifiedOn,
       (      SELECT Source.Id AS 'id',
                     Source.ExternalSystemCode AS 'externalSystemCode',
                     Source.Code AS 'code',
                     Source.ExternalCode AS 'externalCode',
                     Source.[Name] AS 'name',
                     Source.PimProductId AS 'pimProductId',

                     Source.CategoryId AS 'category.id',
                     Source.CategoryCode AS 'category.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Source.CategoryId 
                         AND Coupling.ExternalSystemId = Source.ExternalSystemId) AS 'category.externalCode',
                     Source.CategoryName AS 'category.name',

                     Source.VendorId AS 'vendor.id',
                     Source.VendorCode AS 'vendor.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Source.VendorId 
                         AND Coupling.ExternalSystemId = Source.ExternalSystemId) AS 'vendor.externalCode',
                     Source.VendorName AS 'vendor.name', 

                     Source.ManufacturerId AS 'manufacturer.id',
                     Source.ManufacturerCode AS 'manufacturer.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Source.ManufacturerId 
                         AND Coupling.ExternalSystemId = Source.ExternalSystemId) AS 'manufacturer.externalCode',
                     Source.ManufacturerName AS 'manufacturer.name', 

                     Source.ColorId AS 'color.id',
                     Source.ColorCode AS 'color.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Source.ColorId 
                         AND Coupling.ExternalSystemId = Source.ExternalSystemId) AS 'color.externalCode',
                     Source.ColorName AS 'color.name',
                     
                     Source.ProductModelId AS 'model.id',
                     Source.ProductModelCode AS 'model.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Source.ProductModelId 
                         AND Coupling.ExternalSystemId = Source.ExternalSystemId) AS 'model.externalCode',
                     Source.ProductModelId AS 'model.name',

                     Source.BatteryId AS 'battery.id',
                     Source.BatteryCode AS 'battery.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Source.BatteryId 
                         AND Coupling.ExternalSystemId = Source.ExternalSystemId) AS 'battery.externalCode',
                     Source.BatteryName AS 'battery.name',

                     Source.TaxGroupId AS 'taxGroup.id',
                     Source.TaxGroupCode AS 'taxGroup.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Source.TaxGroupId 
                         AND Coupling.ExternalSystemId = Source.ExternalSystemId) AS 'taxGroup.externalCode',
                     Source.TaxGroupName AS 'taxGroup.name', 

                     Source.VendorItemNo AS 'vendorItemNo',
                     Source.GTIN AS 'gtin',
                     Source.UnitCost AS 'unitCost',
                     Source.IsSerialized AS 'isSerialized',
                     Source.IsInventory AS 'isInventory',
                     Source.ImageUrl AS 'imageUrl'

                 FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
       ) AS JSON
  FROM Source
