﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Data;

namespace WSA.Retail.Integration.Base;

public class ExternalSystemSubscriber
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string? ExternalSystemCode { get; set; }
    public string? ExternalSystemName { get; set; }
    public string? BaseUrl { get; set; }
    public string? EntityCode { get; set; }
    public string? EntityName { get; set; }
    public bool? FromExternalSystem { get; set; }
    public bool? ToExternalSystem { get; set; }


    public static List<ExternalSystemSubscriber> GetExternalSystems(string externalSystemTypeCode, string entityCode)
    {
        List<ExternalSystemSubscriber> result = [];
        
        string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
        ArgumentNullException.ThrowIfNull(connString);

        using SqlConnection conn = new(connString);
        {
            conn.Open();

            using SqlCommand cmd = conn.CreateCommand();
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = "dbo.GetExternalSystemSubscribers";
                cmd.Parameters.AddWithValue("@ExternalSystemTypeCode", externalSystemTypeCode);
                cmd.Parameters.AddWithValue("@EntityCode", entityCode);

                using SqlDataReader reader = cmd.ExecuteReader();
                {
                    while (reader.Read())
                    {
                        ExternalSystemSubscriber subscriber = new()
                        {
                            Id = reader.GetGuid(reader.GetOrdinal("Id")),
                            ExternalSystemCode = reader.GetString(reader.GetOrdinal("ExternalSystemCode")),
                            ExternalSystemName = reader.GetString(reader.GetOrdinal("ExternalSystemName")),
                            BaseUrl = reader.GetString(reader.GetOrdinal("BaseUrl")),
                            EntityCode = reader.GetString(reader.GetOrdinal("EntityCode")),
                            EntityName = reader.GetString(reader.GetOrdinal("EntityName")),
                            FromExternalSystem = reader.GetBoolean(reader.GetOrdinal("FromExternalSystem")),
                            ToExternalSystem = reader.GetBoolean(reader.GetOrdinal("ToExternalSystem"))
                        };
                        result.Add(subscriber);
                    }
                }
            }
        }

        return result;
    }


    public static ExternalSystemSubscriber? GetExternalSystemSubscriberByCode(string externalSystemCode, string entityCode, ILogger _logger)
    {
        _logger.LogInformation("[ExternalSystemSubscriber].[ExternalSystemSubscriber] Procedure started");

        string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
        ArgumentNullException.ThrowIfNull(connString);

        using SqlConnection conn = new(connString);
        {
            conn.Open();

            using SqlCommand cmd = conn.CreateCommand();
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = "dbo.GetExternalSystemSubscribers";
                cmd.Parameters.AddWithValue("@ExternalSystemCode", externalSystemCode);
                cmd.Parameters.AddWithValue("@EntityCode", entityCode);

                using SqlDataReader reader = cmd.ExecuteReader();
                {
                    if (reader.HasRows)
                    {
                        if (reader.Read())
                        {
                            ExternalSystemSubscriber subscriber = new()
                            {
                                Id = reader.GetGuid(reader.GetOrdinal("Id")),
                                ExternalSystemCode = reader.GetString(reader.GetOrdinal("ExternalSystemCode")),
                                ExternalSystemName = reader.GetString(reader.GetOrdinal("ExternalSystemName")),
                                BaseUrl = reader.GetString(reader.GetOrdinal("BaseUrl")),
                                EntityCode = reader.GetString(reader.GetOrdinal("EntityCode")),
                                EntityName = reader.GetString(reader.GetOrdinal("EntityName")),
                                FromExternalSystem = reader.GetBoolean(reader.GetOrdinal("FromExternalSystem")),
                                ToExternalSystem = reader.GetBoolean(reader.GetOrdinal("ToExternalSystem"))
                            };
                            return(subscriber);
                        }
                    }
                }
            }
        }

        return null;
    }
}

