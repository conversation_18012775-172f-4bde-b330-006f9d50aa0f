﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model;

public class Adjustment
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("externalSystemCode")]
    public string? ExternalSystemCode { get; set; }

    [JsonPropertyName("number")]
    public string? Number { get; set; }

    [JsonPropertyName("externalReference")]
    public string? ExternalReference { get; set; }

    [JsonPropertyName("product")]
    public ExternalReference? Product { get; set; }

    [JsonPropertyName("clinic")]
    public ExternalReference? Clinic { get; set; }

    [JsonPropertyName("adjustmentDate")]
    public DateOnly? AdjustmentDate { get; set; }

    [JsonPropertyName("quantity")]
    public decimal? Quantity { get; set; }

    [JsonPropertyName("serialNumber")]
    public string? SerialNumber { get; set; }


    public Adjustment() 
    {
    }
}
