﻿CREATE VIEW [dbo].[Batteries]
AS

  WITH Source AS (
SELECT Battery.Id,
       ExternalSystem.Code AS ExternalSystemCode,
       Battery.Code,
       LatestCouplings.ExternalCode,
       Battery.[Name],
       Battery.CreatedOn,
       Battery.ModifiedOn

  FROM dbo.Battery

 CROSS JOIN dbo.ExternalSystem

 OUTER APPLY (
       SELECT TOP 1 Coupling.ExternalRecordId AS ExternalCode
         FROM dbo.Coupling
        WHERE Coupling.ExternalSystemId = ExternalSystem.Id
          AND Coupling.RecordId = Battery.Id
        ORDER BY ModifiedOn DESC) AS LatestCouplings
     )

SELECT Source.Id,
       Source.ExternalSystemCode,
       Source.Code,
       Source.ExternalCode,
       Source.[Name],
       Source.[CreatedOn],
       Source.[ModifiedOn],
       (
       SELECT Source.Id AS 'id',
              Source.ExternalSystemCode AS 'externalSystemCode',
              Source.Code AS 'code',
              Source.ExternalCode AS 'externalCode',
              Source.[Name] AS 'name',
              Source.CreatedOn AS 'createdOn',
              Source.ModifiedOn AS 'modifiedOn'

           FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
       ) AS JSON
  FROM Source