﻿CREATE PROCEDURE [pim].[ProductParentUpsert]
(
@source pim.[ProductParentUpsertTableType] READONLY,

-- Add output parameters for diagnostics
@rowsReceived INT = NULL OUTPUT,
@diagnosticMessage NVARCHAR(MAX) = NULL OUTPUT
)
AS

BEGIN
DECLARE @rowCount INT;
DECLARE @initialCount INT;
DECLARE @diagnosticLog NVARCHAR(MAX) = '';
DECLARE @firstMergeAffected INT;
DECLARE @finalCount INT;

-- Populate variables
SELECT @rowCount = COUNT(*) FROM @source;
SELECT @initialCount = COUNT(*) FROM pim.Product;

-- Build diagnostic info
SET @diagnosticLog = 'Received ' + CAST(@rowCount AS VARCHAR(20)) + ' rows in @source parameter' + CHAR(13) + CHAR(10) +
                    'Current pim.Product count: ' + CAST(@initialCount AS VARCHAR(20));
    
BEGIN TRY
SET XACT_ABORT ON
BEGIN TRANSACTION

 MERGE pim.ProductParent AS Target
 USING (SELECT ProductId, Product.Id AS ParentId
          FROM (SELECT DISTINCT ProductId, [ParentSku] 
                  FROM @source) AS s1
                 INNER JOIN pim.Product
                    ON s1.[ParentSku] = Product.Sku) AS Source
    ON Source.ProductId = Target.ProductId
   AND Source.ParentId = Target.ParentId

  WHEN NOT MATCHED BY Target 
  THEN
INSERT (ProductId, ParentId)
VALUES (Source.ProductId, Source.ParentId);

   SET @firstMergeAffected = @@rowCount;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Rows affected by MERGE: ' + CAST(@firstMergeAffected AS VARCHAR(20));


COMMIT TRANSACTION;

-- Final counts
SELECT @finalCount = COUNT(*) FROM pim.ProductParent;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Final pim.ProductParent count: ' + CAST(@finalCount AS VARCHAR(20))

   END TRY
 BEGIN CATCH
    IF @@TRANCOUNT > 0
       ROLLBACK TRANSACTION;
            
       SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Error occurred: ' + ERROR_MESSAGE();
       SET @diagnosticMessage = @diagnosticLog;
       THROW;
   END CATCH
    
   SET @diagnosticMessage = @diagnosticLog;
END