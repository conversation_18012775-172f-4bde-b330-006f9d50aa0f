﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace WSA.Retail.Integration.Cosium
{
    public class Product : Model.Product
    {
        public Product() { }

        public Product(string requestBody): base(requestBody) { }

        private static HttpClient? _client;

        public static async Task ProcessNewRecordsAsync(ILogger log)
        {
            var scope = InitScope(nameof(ProcessNewRecordsAsync));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(ProcessNewRecordsAsync));

            List<Product?> products = await GetFromSource(log);
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] Fetched {recordCount} products from Cosium database",
                ClassName(), nameof(ProcessNewRecordsAsync), products?.Count);

            if (products?.Count > 0)
            {
                await PostListAsync(products, log);
            }
            else
            {
                log.BeginScope(scope);
                log.LogInformation("[{className}].[{procedureName}] No new products are avalable in the Cosium database.",
                    ClassName(), nameof(ProcessNewRecordsAsync));
            }
        }

        private static async Task<List<Product?>> GetFromSource(ILogger log)
        {
            var scope = InitScope(nameof(GetFromSource));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromSource));

            List<Product?> productList = [];

            using SqlConnection conn = new(Common.SqlConnectionString());
            { 
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "SELECT * FROM cosium.Products WHERE IntegrationRequired = 1";
                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        {
                            if (reader.HasRows)
                            {
                                DataTable dt = new();
                                dt.Load(reader);
                                log.LogInformation("[{className}].[{procedureName}] SQL reader returned {rowCount} rows.",
                                    ClassName(), nameof(GetFromSource), dt.Rows.Count);

                                foreach (DataRow dr in dt.Rows)
                                {
                                    Product newProduct = new((string)dr["JSON"]);
                                    productList.Add(newProduct);
                                }
                                return productList;
                            }
                            else
                            {
                                log.LogInformation("[{className}].[{procedureName}] No new products were retrieved from the database.",
                                    ClassName(), nameof(GetFromSource));
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.BeginScope(scope);
                    log.LogError(ex, "[{className}].[{procedureName}] Attempt to fetch clinics from the database generated an exception:\r\n{object}",
                        ClassName(), nameof(GetFromSource), ex.Message);
                }
            }
            return productList;
        }

        public static async Task<Product?> GetFromBaseAsync(ILogger log, string? code = null, string? externalCode = null)
        {
            var scope = InitScope(nameof(GetFromBaseAsync));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromBaseAsync));

            Product? product = new();
            if (await product.GetAsync(scope, log, Common.SqlConnectionString(), Common.ExternalSystemCode(), code: code, externalCode: externalCode))
            {
                if (product.Id != null)
                {
                    return product;
                }
            }
            return null;
        }

        public static async Task<Product?> GetFromDatabaseById(int thisId, ILogger log)
        {
            var scope = InitScope(nameof(GetFromDatabaseById));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(GetFromDatabaseById));

            using SqlConnection conn = new(Common.SqlConnectionString());
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "SELECT * FROM cosium.Products WHERE ExternalCode = @thisId";
                        cmd.Parameters.AddWithValue("@thisId", thisId);

                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        {
                            if (reader.HasRows)
                            {
                                DataTable dt = new();
                                dt.Load(reader);
                                log.LogInformation("[{className}].[{procedureName}] SQL reader returned {rowCount} rows",
                                    ClassName(), nameof(GetFromDatabaseById), dt.Rows.Count);

                                DataRow dr = dt.Rows[0];
                                Product? newProduct = new((string)dr["JSON"]);

                                return newProduct;
                            }
                            else
                            {
                                log.LogInformation("[{className}].[{procedureName}] No new products were retrieved from the database.",
                                    ClassName(), nameof(GetFromDatabaseById));
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.BeginScope(scope);
                    log.LogError(ex, "[{className}].[{procedureName}] Attempt to fetch products from the database generated an exception:\r\n{object}",
                        ClassName(), nameof(GetFromDatabaseById), ex.Message);
                }
            }
            return null;
        }

        public static async Task PostListAsync(List<Product?> products, ILogger log)
        {
            var scope = InitScope(nameof(PostListAsync));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(PostListAsync));

            var entitySubscriber = await EntitySubscriber.GetAsync(null, Common.ExternalSystemCode(), "PRODUCT");
            ArgumentNullException.ThrowIfNull(entitySubscriber);
            if (entitySubscriber.FromExternalSystem ?? false == true)
            {
                try
                {
                    foreach (var product in products)
                    {
                        if (product != null)
                        {
                            await product.PostAsync(log, entitySubscriber);
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.BeginScope(scope);
                    log.LogError(ex, "[{className}].[{procedureName}] Encountered an exception:\r\n{object}",
                        ClassName(), nameof(PostListAsync), ex.Message);
                }
            }
        }

        public async Task<Product?> PostAsync(ILogger log, Model.EntitySubscriber? entitySubscriber = null)
        {
            var scope = InitScope(nameof(PostAsync));
            AddEntityToScope(ref scope, this);
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(PostAsync));

            if (entitySubscriber == null)
            {
                entitySubscriber = await EntitySubscriber.GetAsync(null, Common.ExternalSystemCode(), "PRODUCT");
                ArgumentNullException.ThrowIfNull(entitySubscriber);
            }
            if (entitySubscriber.FromExternalSystem ?? false == true)
            {
                _client ??= Common.GetHttpClient();

                try
                {
                    if (this != null)
                    {
                        AddEntityToScope(ref scope, this);
                        await this.ValidateExternalReferences(log);
                        var requestBody = JsonSerializer.Serialize(this, Cosium.Common.GetJsonOptions());
                        var content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                        var request = new HttpRequestMessage(HttpMethod.Post, "products")
                        {
                            Content = content
                        };

                        log.LogInformation("[{className}].[{procedureName}] Sending product: {code} to API\r\n{requestBody}",
                            ClassName(), nameof(PostAsync), this.Code, requestBody);
                        HttpResponseMessage response = await _client.SendAsync(request);
                        var responseBody = await response.Content.ReadAsStringAsync();
                        responseBody = JsonObject.Parse(responseBody)?.ToJsonString(Common.GetJsonOptions()) ?? "";
                        log.LogInformation("[{className}].[{procedureName}] API response:\r\n{status}\r\nBody:\r\n{responseBody}",
                            ClassName(), nameof(PostAsync), response.StatusCode.ToString() + " - " + response.RequestMessage, responseBody);

                        if (response.StatusCode == System.Net.HttpStatusCode.OK)
                        {
                            log.LogInformation("[{className}].[{procedureName}] Successfully updated product: {code}",
                                ClassName(), nameof(PostAsync), this.Code);
                            var product = new Product(responseBody);
                            if (product != null)
                            {
                                UpdateIsIntegrated();
                                return product;
                            }
                        }
                        else
                        {
                            log.LogError("[{className}].[{procedureName}] Attempt to update clinic: {code} failed\r\nMessage:{responseBody}",
                                ClassName(), nameof(PostAsync), this.Code, responseBody);
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.BeginScope(scope);
                    log.LogError(ex, "[{className}].[{procedureName}] Encountered an exception:\r\n{object}",
                        ClassName(), nameof(PostAsync), ex.Message);
                }
            }
            return null;
        }

        public async Task ValidateExternalReferences(ILogger log)
        {
            await ValidateCategory(log);
            await ValidateSubcategory(log);
            await Cosium.Vendor.ValidateExternalReference(Vendor, log);
            await Cosium.TaxGroup.ValidateExternalReference(TaxGroup);
        }

        public async Task ValidateCategory(ILogger log)
        {
            var scope = InitScope(nameof(PostAsync));
            AddEntityToScope(ref scope, this);
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(PostAsync));

            if (Category != null)
            {
                ArgumentNullException.ThrowIfNull(Category.ExternalCode);
                Cosium.Category? record = await Cosium.Category.GetAsync(externalCode: Category.ExternalCode);

                ArgumentNullException.ThrowIfNull(record);
                Category.Id = record.Id;
                Category.ExternalCode = record.ExternalCode;
                Category.Code = record.Code;
                Category.Name = record.Name;
            }
        }

        public async Task ValidateSubcategory(ILogger log)
        {
            var scope = InitScope(nameof(PostAsync));
            AddEntityToScope(ref scope, this);
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(PostAsync));

            if (Subcategory != null)
            {
                ArgumentNullException.ThrowIfNull(Subcategory.ExternalCode);
                Cosium.Subcategory? record = await Cosium.Subcategory.GetAsync(externalCode: Subcategory.ExternalCode);

                ArgumentNullException.ThrowIfNull(record);
                Subcategory.Id = record.Id;
                Subcategory.ExternalCode = record.ExternalCode;
                Subcategory.Code = record.Code;
                Subcategory.Name = record.Name;
            }
        }

        private bool UpdateIsIntegrated()
        {
            using SqlConnection conn = new(Common.SqlConnectionString());
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "UPDATE cosium.Products SET IntegrationRequired = 0, IntegrationDate = sysutcdatetime() WHERE ExternalCode = @id";
                        cmd.Parameters.AddWithValue("@id", this.ExternalCode);
                        cmd.ExecuteNonQuery();
                        conn.Close();
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            }
        }

        internal static async Task ValidateExternalReference(Model.ExternalReference? externalReference, ILogger log)
        {
            var scope = InitScope(nameof(ValidateExternalReference));
            log.BeginScope(scope);
            log.LogInformation("[{className}].[{procedureName}] started", ClassName(), nameof(ValidateExternalReference));

            if (externalReference != null && externalReference.ExternalCode != null)
            {
                ArgumentNullException.ThrowIfNull(externalReference.ExternalCode);
                Product? product = await GetFromBaseAsync(log, externalCode: externalReference.ExternalCode);
                {
                    if (product == null)
                    {
                        product = await GetFromDatabaseById(int.Parse(externalReference.ExternalCode), log);
                        if (product != null)
                        {
                            product = await product.PostAsync(log);
                        }
                    }
                }

                ArgumentNullException.ThrowIfNull(product);
                ArgumentNullException.ThrowIfNull(product.Id);
                externalReference.Id = product.Id;
                externalReference.ExternalCode = product.ExternalCode;
                externalReference.Code = product.Code;
                externalReference.Name = product.Name;
            }
        }

        private static string ClassName()
        {
            return nameof(Product);
        }

        private static Dictionary<string, object> InitScope(string procedureName)
        {
            return new Dictionary<string, object>() {
                { "appName", "Cosium" },
                { "className", nameof(Product) },
                { "procedureName", procedureName }
            };
        }

        private static void AddEntityToScope(ref Dictionary<string, object> scope, Product product)
        {
            scope["entityName"] = "Product";
            scope["entityCode"] = product.Code ?? "";
        }
    }
}
