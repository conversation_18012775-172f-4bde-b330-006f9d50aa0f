﻿CREATE PROCEDURE [pim].[BrandUpsert]
(
    @source [pim].[BrandUpsertTableType] READONLY,

    -- Add output parameters for diagnostics
    @rowsReceived INT = NULL OUTPUT,
    @diagnosticMessage NVARCHAR(MAX) = NULL OUTPUT
)
AS
BEGIN
DECLARE @distinctBrandCount INT;
DECLARE @currentBrandCount INT;
DECLARE @currentProductBrandCount INT;
DECLARE @firstMergeAffected INT;
DECLARE @secondMergeAffected INT;
DECLARE @finalBrandCount INT;
DECLARE @finalProductBrandCount INT;
DECLARE @diagnosticLog NVARCHAR(MAX) = '';

-- Populate variables
SELECT @rowsReceived = COUNT(*) FROM @source;
SELECT @distinctBrandCount = COUNT(DISTINCT Code) FROM @source;
SELECT @currentBrandCount = COUNT(*) FROM pim.Brand;
SELECT @currentProductBrandCount = COUNT(*) FROM pim.ProductBrand;


-- Build diagnostic info
SET @diagnosticLog = 'Received ' + CAST(@rowsReceived AS VARCHAR(20)) + ' rows in @source parameter' + CHAR(13) + CHAR(10) +
                    'Distinct brand codes in @source: ' + CAST(@distinctBrandCount AS VARCHAR(20)) + CHAR(13) + CHAR(10) +
                    'Current pim.Brand count: ' + CAST(@currentBrandCount AS VARCHAR(20)) + CHAR(13) + CHAR(10) +
                    'Current pim.ProductBrand count: ' + CAST(@currentProductBrandCount AS VARCHAR(20));

 BEGIN TRY
   SET XACT_ABORT ON
 BEGIN TRANSACTION

 MERGE pim.Brand AS Target
 USING (SELECT DISTINCT Code 
          FROM @source) AS Source
    ON Source.Code = Target.Code
  
  WHEN NOT MATCHED BY Target 
  THEN
INSERT (Code)
VALUES (Source.Code);

   SET @firstMergeAffected = @@rowCount;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Rows affected by pim.Brand MERGE: ' + CAST(@firstMergeAffected AS VARCHAR(20));


 MERGE pim.ProductBrand AS Target
 USING (SELECT s1.ProductId,
               Brand.Id AS BrandId
          FROM (SELECT DISTINCT ProductId, Code FROM @source) AS s1
         INNER JOIN pim.Brand
            ON s1.Code = Brand.Code) AS Source
    ON Source.ProductId = Target.ProductId
   AND Source.BrandId = Target.BrandId
   
  WHEN NOT MATCHED BY Target 
  THEN
INSERT (ProductId, BrandId)
VALUES (Source.ProductId, Source.BrandId);

   SET @secondMergeAffected = @@rowCount;
   SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Rows affected by pim.ProductBrand MERGE: ' + CAST(@secondMergeAffected AS VARCHAR(20));


COMMIT TRANSACTION;

-- Final counts
SELECT @finalBrandCount = COUNT(*) FROM pim.Brand;
SELECT @finalProductBrandCount = COUNT(*) FROM pim.ProductBrand;
SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Final pim.Brand count: ' + CAST(@finalBrandCount AS VARCHAR(20))
SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Final pim.ProductBrand count: ' + CAST(@finalProductBrandCount AS VARCHAR(20));
  

   END TRY
 BEGIN CATCH
    IF @@TRANCOUNT > 0
       ROLLBACK TRANSACTION;
            
       SET @diagnosticLog = @diagnosticLog + CHAR(13) + CHAR(10) + 'Error occurred: ' + ERROR_MESSAGE();
       SET @diagnosticMessage = @diagnosticLog;
       THROW;
   END CATCH
    
   SET @diagnosticMessage = @diagnosticLog;
END