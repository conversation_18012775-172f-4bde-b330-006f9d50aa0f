<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <DebugSymbols>false</DebugSymbols>
    <SkipCopyBuildProduct>true</SkipCopyBuildProduct>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <TargetRuntime>None</TargetRuntime>
    <BaseIntermediateOutputPath Condition="'$(BaseIntermediateOutputPath)'=='' ">obj\</BaseIntermediateOutputPath>
    <BaseIntermediateOutputPath Condition=" !HasTrailingSlash('$(BaseIntermediateOutputPath)') ">$(BaseIntermediateOutputPath)\</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)$(Configuration)\</IntermediateOutputPath>
    <ProjectReferencesOutputPath Condition=" '$(ProjectReferencesOutputPath)' == '' ">$(IntermediateOutputPath)ProjectReferences</ProjectReferencesOutputPath>
    <ProjectReferencesOutputPath Condition=" !HasTrailingSlash('$(ProjectReferencesOutputPath)') ">$(ProjectReferencesOutputPath)\</ProjectReferencesOutputPath>
    <StageArtifacts Condition=" '$(StageArtifacts)' == '' ">true</StageArtifacts>
  </PropertyGroup>

  <PropertyGroup>
    <DefineCommonItemSchemas>false</DefineCommonItemSchemas>
    <DefineCommonCapabilities>false</DefineCommonCapabilities>
  </PropertyGroup>

  <ProjectExtensions>
    <ProjectCapabilities>
      <DeploymentProject />
    </ProjectCapabilities>
  </ProjectExtensions>

  <ItemDefinitionGroup>
    <Content>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
    <ProjectReference>
      <Private>false</Private>
      <Targets>Build</Targets>
    </ProjectReference>
  </ItemDefinitionGroup>

  <Target Name="CreateManifestResourceNames" />

  <PropertyGroup>
    <StageArtifactsDependsOn>
      _GetDeploymentProjectContent;
      _CalculateContentOutputRelativePaths;
      _GetReferencedProjectsOutput;
      _CalculateArtifactStagingDirectory;
      _CopyOutputToArtifactStagingDirectory;
    </StageArtifactsDependsOn>
  </PropertyGroup>

  <Target Name="_CopyOutputToArtifactStagingDirectory">
    <Copy SourceFiles="@(DeploymentProjectContentOutput)" DestinationFiles="$(ArtifactStagingDirectory)\$(MSBuildProjectName)%(RelativePath)" />
    <Copy SourceFiles="@(BuildProjectReferencesOutput)" DestinationFiles="$(ArtifactStagingDirectory)\$(MSBuildProjectName)\%(ProjectName)\%(RecursiveDir)%(FileName)%(Extension)" />
  </Target>

  <Target Name="_GetDeploymentProjectContent">
    <MSBuild Projects="$(MSBuildProjectFile)" Targets="ContentFilesProjectOutputGroup">
      <Output TaskParameter="TargetOutputs" ItemName="DeploymentProjectContentOutput" />
    </MSBuild>
  </Target>

  <Target Name="_GetReferencedProjectsOutput">
    <PropertyGroup>
      <MsBuildProperties>Configuration=$(Configuration);Platform=$(Platform)</MsBuildProperties>
    </PropertyGroup>

    <MSBuild Projects="@(ProjectReference)"
             BuildInParallel="$(BuildInParallel)"
             Properties="$(MsBuildProperties)"
             Targets="%(ProjectReference.Targets)" />

    <ItemGroup>
      <BuildProjectReferencesOutput Include="%(ProjectReference.IncludeFilePath)">
        <ProjectName>$([System.IO.Path]::GetFileNameWithoutExtension('%(ProjectReference.Identity)'))</ProjectName>
      </BuildProjectReferencesOutput>
    </ItemGroup>
  </Target>

  <Target Name="_CalculateArtifactStagingDirectory" Condition=" '$(ArtifactStagingDirectory)'=='' ">
    <PropertyGroup>
      <ArtifactStagingDirectory Condition=" '$(OutDir)'!='' ">$(OutDir)</ArtifactStagingDirectory>
      <ArtifactStagingDirectory Condition=" '$(ArtifactStagingDirectory)'=='' ">$(OutputPath)</ArtifactStagingDirectory>
      <ArtifactStagingDirectory Condition=" !HasTrailingSlash('$(ArtifactStagingDirectory)') ">$(ArtifactStagingDirectory)\</ArtifactStagingDirectory>
      <ArtifactStagingDirectory>$(ArtifactStagingDirectory)staging\</ArtifactStagingDirectory>
      <ArtifactStagingDirectory Condition=" '$(Build_StagingDirectory)'!='' AND '$(TF_Build)'=='True' ">$(Build_StagingDirectory)</ArtifactStagingDirectory>
    </PropertyGroup>
  </Target>

  <!-- Appends each of the deployment project's content output files with metadata indicating its relative path from the deployment project's folder. -->
  <Target Name="_CalculateContentOutputRelativePaths"
          Outputs="%(DeploymentProjectContentOutput.Identity)">
    <PropertyGroup>
      <_OriginalIdentity>%(DeploymentProjectContentOutput.Identity)</_OriginalIdentity>
      <_RelativePath>$(_OriginalIdentity.Replace('$(MSBuildProjectDirectory)', ''))</_RelativePath>
    </PropertyGroup>

    <ItemGroup>
      <DeploymentProjectContentOutput>
        <RelativePath>$(_RelativePath)</RelativePath>
      </DeploymentProjectContentOutput>
    </ItemGroup>
  </Target>

  <Target Name="CoreCompile" />

  <PropertyGroup>
    <StageArtifactsAfterTargets Condition=" '$(StageArtifacts)' == 'true' ">
      PrepareForRun
    </StageArtifactsAfterTargets>
  </PropertyGroup>

  <Target Name="StageArtifacts" DependsOnTargets="$(StageArtifactsDependsOn)" AfterTargets="$(StageArtifactsAfterTargets)"/>

  <!-- Custom target to clean up local deployment staging files -->
  <Target Name="DeleteBinObjFolders" BeforeTargets="Clean">
    <RemoveDir Directories="$(OutputPath)" />  
    <RemoveDir Directories="$(BaseIntermediateOutputPath)" />
  </Target>
</Project>
