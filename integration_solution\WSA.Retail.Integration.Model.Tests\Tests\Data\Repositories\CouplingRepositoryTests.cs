﻿using Moq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WSA.Retail.Integration.Configuration;
using WSA.Retail.Integration.Data;
using WSA.Retail.Integration.Data.Repositories;
using WSA.Retail.Integration.Models.Configuration;
using WSA.Retail.Integration.Models.Couplings;
using WSA.Retail.Integration.Tests.Configuration;
using WSA.Retail.Integration.Data.Repositories.Interfaces;
using WSA.Retail.Integration.Tests.Data;
using WSA.Retail.Integration.Models.References;
using WSA.Retail.Integration.Models.Patients;

namespace WSA.Retail.Integration.Tests.Data.Repositories;

public class CouplingRepositoryTests
{
    private readonly AppSettings _appSettings;

    public CouplingRepositoryTests()
    {
        _appSettings = TestAppSettings.CreateDefault();
    }

    private void SeedReferenceData(TestIntegrationContext context, Guid externalSystemId, Guid entityId)
    {
        // Seed ExternalSystem entities
        var externalSystems = new List<ExternalSystemEntity>
        {
            new() { Id = Guid.NewGuid(), Code = "TEST1", Name = "Test System 1" },
            new() { Id = externalSystemId, Code = "TEST", Name = "Test System 2" }
        };

        // Seed Entity entities
        var entities = new List<EntityEntity>
        {
            new() { Id = entityId, Code = EntityType.Patient.GetEntityCode(), Name = "Patient" }
        };

        context.ExternalSystemEntity.AddRange(externalSystems);
        context.EntityEntity.AddRange(entities);
        context.SaveChanges();
    }

    private CouplingRepository CreateRepository(string dbName)
    {
        var optionsMock = new Mock<IOptions<AppSettings>>();
        optionsMock.Setup(o => o.Value).Returns(_appSettings);

        var loggerMock = new Mock<ILogger<CouplingRepository>>();

        var dbContextFactoryMock = new Mock<IDbContextFactory<IntegrationContext>>();
        dbContextFactoryMock
            .Setup(f => f.CreateDbContext())
            .Returns(() => TestDbContextHelper.GetInMemoryDbContext(dbName));

        return new CouplingRepository(
            optionsMock.Object,
            loggerMock.Object,
            dbContextFactoryMock.Object);
    }


    [Fact]
    public async Task GetAsync_GetByIdShouldReturnCoupling()
    {
        // Arrange
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);

        var externalSystemId = Guid.NewGuid();
        var entityId = Guid.NewGuid();
        var recordId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa");

        // Seed reference data
        SeedReferenceData(dbContext, externalSystemId, entityId);

        // Add coupling for the test
        var coupling = new CouplingEntity
        {
            Id = Guid.NewGuid(),
            ExternalSystemId = Guid.NewGuid(), // Different external system for TEST1
            EntityId = entityId,
            RecordId = recordId,
            ExternalRecordId = "EXT1-001",
            CreatedOn = DateTime.UtcNow.AddDays(-10),
            ModifiedOn = DateTime.UtcNow.AddDays(-5)
        };

        // Add the TEST1 external system
        var test1ExternalSystem = new ExternalSystemEntity
        {
            Id = coupling.ExternalSystemId.Value,
            Code = "TEST1",
            Name = "Test System 1"
        };
        dbContext.ExternalSystemEntity.Add(test1ExternalSystem);
        dbContext.CouplingEntity.Add(coupling);
        dbContext.SaveChanges();

        var repository = CreateRepository(dbName);

        // Act
        var result = await repository.GetAsync(
            "TEST1",
            EntityType.Patient.GetEntityCode(),
            recordId: recordId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("EXT1-001", result.ExternalCode);
    }


    [Fact]
    public async Task GetAsync_GetByExternalCodeShouldReturnCoupling()
    {
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);

        var externalSystemId = Guid.NewGuid();
        var entityId = Guid.NewGuid();

        // Seed reference data first
        SeedReferenceData(dbContext, externalSystemId, entityId);

        // Create test patient directly
        var testPatientId = Guid.NewGuid();
        var testPatient = new PatientEntity
        {
            Id = testPatientId,
            Code = "TEST001",
            Name = "Test Patient",
            CreatedOn = DateTime.UtcNow,
            ModifiedOn = DateTime.UtcNow
        };
        dbContext.PatientEntity.Add(testPatient);

        // Create coupling for the test patient
        var coupling = new CouplingEntity
        {
            Id = Guid.NewGuid(),
            ExternalSystemId = externalSystemId, // Using TEST system
            EntityId = entityId,
            RecordId = testPatientId,
            ExternalRecordId = "TEST001",
            CreatedOn = DateTime.UtcNow,
            ModifiedOn = DateTime.UtcNow
        };
        dbContext.CouplingEntity.Add(coupling);
        dbContext.SaveChanges();

        // Act
        var repository = CreateRepository(dbName);
        var result = await repository.GetAsync(
            _appSettings.ExternalSystemCode,
            EntityType.Patient.GetEntityCode(),
            externalCode: "TEST001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(testPatientId, result.RecordId);
    }

    [Fact]
    public async Task UpdateAsync_InsertAsyncShouldReturnTrue()
    {
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);

        var externalSystemId = Guid.NewGuid();
        var entityId = Guid.NewGuid();

        // Seed reference data first
        SeedReferenceData(dbContext, externalSystemId, entityId);

        // Create test patient directly
        var testPatientId = Guid.NewGuid();
        var testPatient = new PatientEntity
        {
            Id = testPatientId,
            Code = "TEST002",
            Name = "Test Patient 2",
            CreatedOn = DateTime.UtcNow,
            ModifiedOn = DateTime.UtcNow
        };
        dbContext.PatientEntity.Add(testPatient);
        dbContext.SaveChanges();

        // Initialize with dependency injection
        var repository = CreateRepository(dbName);

        // Create coupling and insert
        var coupling = new Coupling
        {
            Entity = new InternalReference { Id = entityId, Code = EntityType.Patient.GetEntityCode() },
            ExternalSystem = new InternalReference { Id = externalSystemId, Code = _appSettings.ExternalSystemCode },
            RecordId = testPatientId,
            ExternalCode = "EXTERNALCODE"
        };
        var success = await repository.InsertAsync(coupling);

        // Assert
        Assert.True(success);
    }

    [Fact]
    public async Task UpdateAsync_UpdateAsyncShouldReturnTrue()
    {
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);

        var externalSystemId = Guid.NewGuid();
        var entityId = Guid.NewGuid();

        // Seed reference data first
        SeedReferenceData(dbContext, externalSystemId, entityId);

        // Create test patient directly
        var testPatientId = Guid.NewGuid();
        var testPatient = new PatientEntity
        {
            Id = testPatientId,
            Code = "TEST003",
            Name = "Test Patient 3",
            CreatedOn = DateTime.UtcNow,
            ModifiedOn = DateTime.UtcNow
        };
        dbContext.PatientEntity.Add(testPatient);

        // Create existing coupling
        var existingCouplingId = Guid.NewGuid();
        var existingCoupling = new CouplingEntity
        {
            Id = existingCouplingId,
            ExternalSystemId = externalSystemId,
            EntityId = entityId,
            RecordId = testPatientId,
            ExternalRecordId = "ORIGINALCODE",
            CreatedOn = DateTime.UtcNow,
            ModifiedOn = DateTime.UtcNow
        };
        dbContext.CouplingEntity.Add(existingCoupling);
        dbContext.SaveChanges();

        // Initialize with dependency injection
        var repository = CreateRepository(dbName);

        // Create coupling and update
        var coupling = new Coupling
        {
            Id = existingCouplingId,
            Entity = new InternalReference { Id = entityId, Code = EntityType.Patient.GetEntityCode() },
            ExternalSystem = new InternalReference { Id = externalSystemId, Code = _appSettings.ExternalSystemCode },
            RecordId = testPatientId,
            ExternalCode = "NEWEXTERNALCODE"
        };
        var success = await repository.UpdateAsync(coupling);

        // Assert
        Assert.True(success);
    }
    [Fact]
    public async Task GetAsync_NonExistentRecordShouldReturnNull()
    {
        // Arrange
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);
        
        var externalSystemId = Guid.NewGuid();
        var entityId = Guid.NewGuid();
        
        // Seed reference data
        SeedReferenceData(dbContext, externalSystemId, entityId);
        
        var repository = CreateRepository(dbName);
        
        // Act - search for non-existent record
        var result = await repository.GetAsync(
            "TEST1",
            EntityType.Patient.GetEntityCode(),
            recordId: Guid.NewGuid());
        
        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetAsync_NonExistentExternalCodeShouldReturnNull()
    {
        // Arrange
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);
        
        var externalSystemId = Guid.NewGuid();
        var entityId = Guid.NewGuid();
        
        // Seed reference data
        SeedReferenceData(dbContext, externalSystemId, entityId);
        
        var repository = CreateRepository(dbName);
        
        // Act - search for non-existent external code
        var result = await repository.GetAsync(
            _appSettings.ExternalSystemCode,
            EntityType.Patient.GetEntityCode(),
            externalCode: "NONEXISTENT");
        
        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task InsertAsync_WithInvalidExternalSystemCodeShouldReturnFalse()
    {
        // Arrange
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);
        
        var entityId = Guid.NewGuid();
        var testPatientId = Guid.NewGuid();
        
        // Seed only entity data
        var entities = new List<EntityEntity>
        {
            new() { Id = entityId, Code = EntityType.Patient.GetEntityCode(), Name = "Patient" }
        };
        dbContext.EntityEntity.AddRange(entities);
        dbContext.SaveChanges();
        
        var repository = CreateRepository(dbName);
        
        // Create coupling with non-existent external system
        var coupling = new Coupling
        {
            Entity = new InternalReference { Id = entityId, Code = EntityType.Patient.GetEntityCode() },
            ExternalSystem = new InternalReference { Code = "NONEXISTENT" },
            RecordId = testPatientId,
            ExternalCode = "EXTERNALCODE"
        };
        
        // Act
        var success = await repository.InsertAsync(coupling);
        
        // Assert
        Assert.False(success);
    }

    [Fact]
    public async Task UpdateAsync_NonExistentCouplingIdShouldReturnFalse()
    {
        // Arrange
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);
        
        var externalSystemId = Guid.NewGuid();
        var entityId = Guid.NewGuid();
        
        // Seed reference data
        SeedReferenceData(dbContext, externalSystemId, entityId);
        
        var repository = CreateRepository(dbName);
        
        // Create coupling with non-existent ID
        var coupling = new Coupling
        {
            Id = Guid.NewGuid(), // Non-existent ID
            Entity = new InternalReference { Id = entityId, Code = EntityType.Patient.GetEntityCode() },
            ExternalSystem = new InternalReference { Id = externalSystemId, Code = _appSettings.ExternalSystemCode },
            RecordId = Guid.NewGuid(),
            ExternalCode = "NEWCODE"
        };
        
        // Act
        var success = await repository.UpdateAsync(coupling);
        
        // Assert
        Assert.False(success);
    }

    [Fact]
    public async Task InsertAsync_DuplicateCouplingForSameRecordShouldHandleAppropriately()
    {
        // Arrange
        var dbName = Guid.NewGuid().ToString();
        using var dbContext = TestDbContextHelper.GetInMemoryDbContext(dbName);
        
        var externalSystemId = Guid.NewGuid();
        var entityId = Guid.NewGuid();
        var testPatientId = Guid.NewGuid();
        
        // Seed reference data
        SeedReferenceData(dbContext, externalSystemId, entityId);
        
        // Create existing coupling
        var existingCoupling = new CouplingEntity
        {
            Id = Guid.NewGuid(),
            ExternalSystemId = externalSystemId,
            EntityId = entityId,
            RecordId = testPatientId,
            ExternalRecordId = "ORIGINALCODE",
            CreatedOn = DateTime.UtcNow,
            ModifiedOn = DateTime.UtcNow
        };
        dbContext.CouplingEntity.Add(existingCoupling);
        dbContext.SaveChanges();
        
        var repository = CreateRepository(dbName);
        
        // Try to insert another coupling for the same record
        var newCoupling = new Coupling
        {
            Entity = new InternalReference { Id = entityId, Code = EntityType.Patient.GetEntityCode() },
            ExternalSystem = new InternalReference { Id = externalSystemId, Code = _appSettings.ExternalSystemCode },
            RecordId = testPatientId,
            ExternalCode = "NEWCODE"
        };
        
        // Act
        var success = await repository.InsertAsync(newCoupling);
        
        // Assert - behavior depends on your implementation
        // If you allow multiple couplings per record, this should be true
        // If you don't, this should be false or throw an exception
        Assert.False(success); // Adjust based on expected behavior
    }
}
