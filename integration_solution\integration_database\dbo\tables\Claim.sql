﻿CREATE TABLE [dbo].[Claim]
(
    [Id]               UNIQUEIDENTIFIER CONSTRAINT [DF_Claim_Id] DEFAULT NEWID() NOT NULL,
    [SalesInvoiceId]   UNIQUEIDENTIFIER NULL,
    [SalesCreditId]    UNIQUEIDENTIFIER NULL,
    [DocumentNumber]   NVARCHAR(20) NOT NULL,
    [PatientId]        UNIQUEIDENTIFIER NOT NULL,
    [PayorId]          UNIQUEIDENTIFIER NOT NULL,
    [ClinicId]         UNIQUEIDENTIFIER NOT NULL,
    [AppliesTo]        NVARCHAR(20) NULL,
    [ReferenceNumber]  NVARCHAR(50) NULL,
    [DocumentDate]     DATE NULL,
    [Amount]           MONEY NOT NULL,
    [CreatedOn]        DATETIME2 CONSTRAINT [DF_Claim_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]       DATETIME2 CONSTRAINT [DF_Claim_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT [PK_Claim_Id] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Claim_SalesInvoice] FOREIGN KEY (SalesInvoiceId) REFERENCES [dbo].[SalesInvoice](Id),
    CONSTRAINT [FK_Claim_SalesCredit] FOREIGN KEY (SalesCreditId) REFERENCES [dbo].[SalesCredit](Id),
    CONSTRAINT [FK_Claim_Patient] FOREIGN KEY (PatientId) REFERENCES [dbo].[Patient](Id),
    CONSTRAINT [FK_Claim_Payor] FOREIGN KEY (PayorId) REFERENCES [dbo].[Payor](Id),
    CONSTRAINT [FK_Claim_Clinic] FOREIGN KEY (ClinicId) REFERENCES [dbo].[Clinic](Id)
)
GO;

CREATE UNIQUE INDEX IX_Claim_Document
ON [dbo].[Claim] ([DocumentNumber])
GO;

CREATE INDEX IX_Claim_SalesInvoice
ON [dbo].[Claim] ([SalesInvoiceId], [Id])
GO;

CREATE INDEX IX_Claim_SalesCredit   
ON [dbo].[Claim] ([SalesCreditId], [Id])
GO;