﻿CREATE TABLE [manage].[EventError]
(
    [Id]                UNIQUEIDENTIFIER CONSTRAINT [DF_EventError_Id] DEFAULT NEWID() NOT NULL,
    [EventType]         NVARCHAR(100) NOT NULL,
    [EventId]           UNIQUEIDENTIFIER NOT NULL,
    [ErrorMessage]      NVARCHAR(MAX),
    [StackTrace]        NVARCHAR(MAX),
    [Stage]             NVARCHAR(50),
    [CreatedOn]         DATETIME2 CONSTRAINT [DF_EventError_CreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [ModifiedOn]        DATETIME2 CONSTRAINT [DF_EventError_ModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT          [PK_EventError_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE INDEX IX_EventError_EventId
ON [manage].[EventError] ([EventId], [CreatedOn])