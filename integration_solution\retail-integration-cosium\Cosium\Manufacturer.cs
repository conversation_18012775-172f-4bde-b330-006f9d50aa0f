﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Text.Json;
using System.Text;

namespace WSA.Retail.Integration.Cosium
{
    public class Manufacturer : Model.Manufacturer
    {
        public Manufacturer() { }

        public Manufacturer(DataRow row)
        {
            Code = row.IsNull("code") ? null : (string?)row["code"];
            ExternalCode = row.IsNull("externalCode") ? null : (string?)row["externalCode"];
            Name = row.IsNull("name") ? null : (string?)row["name"];
            Address = row.IsNull("address") ? null : (string?)row["address"];
            Address2 = row.IsNull("address2") ? null : (string?)row["address2"];
            City = row.IsNull("city") ? null : (string?)row["city"];
            Country = row.IsNull("country") ? null : (string?)row["country"];
            PostalCode = row.IsNull("postalCode") ? null : (string?)row["postalCode"];
            Phone = row.IsNull("phone") ? null : (string?)row["phone"];
        }

        public static async Task ProcessNewRecordsAsync(ILogger log)
        {
            log.LogInformation("[Cosium].[Manufacturer].[ProcessNewRecordsAsync] Procedure started");
            List<Manufacturer?> manufacturers = await GetFromDatabase(log);
            log.LogDebug("[Cosium].[Manufacturer].[ProcessNewRecordsAsync] Number of new records fetched from Cosium database is: {recordCount}", manufacturers?.Count);
            if (manufacturers?.Count > 0)
            {
                await PostAsync(manufacturers, log);
            }
            else
            {
                log.LogDebug("[Cosium].[Manufacturer].[ProcessNewRecordsAsync] No new records are avalable in the Cosium database.");
            }
            log.LogDebug("[Cosium].[Manufacturer].[ProcessNewRecordsAsync] Procedure completed");
        }

        private static async Task<List<Manufacturer?>> GetFromDatabase(ILogger log)
        {
            log.LogDebug("[Cosium].[Manufacturer].[GetFromDatabase] Procedure started");

            List<Manufacturer?> manufacturerList = [];

            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);

            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.CommandText = "cosium.GetManufacturers";

                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        {
                            log.LogDebug("[Cosium].[Manufacturer].[GetFromDatabase] SQL command executed");
                            if (reader.HasRows)
                            {
                                DataTable dt = new();
                                dt.Load(reader);
                                log.LogDebug("[Cosium].[Manufacturer].[GetFromDatabase] SQL reader has {rowCount} rows", dt.Rows.Count);

                                foreach (DataRow dr in dt.Rows)
                                {
                                    Manufacturer newManufacturer = new(dr);
                                    manufacturerList.Add(newManufacturer);
                                }
                                log.LogDebug("[Cosium].[Manufacturer].[GetFromDatabase] Procedure completed");
                                return manufacturerList;
                            }
                            else
                            {
                                log.LogTrace("[Cosium].[Manufacturer].[GetFromDatabase] No new patients were retrieved from the database.");
                            }
                        }
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "[Cosium].[Manufacturer].[GetFromDatabase] Attempt to fetch records from the database generated an exception:\r\n{object}", ex.Message);
                }
            }
            return manufacturerList;
        }

        public static async Task<List<Manufacturer?>> GetFromDatabaseByExternalCode(string externalCode, ILogger log)
        {
            log.LogDebug("[Cosium].[Manufacturer].[GetFromDatabaseByExternalCode] Procedure started");

            var manufacturerList = new List<Manufacturer?>();

            var connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);

            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "SELECT * FROM cosium.Manufacturers WHERE ExternalCode = @externalCode";
                        cmd.Parameters.AddWithValue("@externalCode", externalCode);

                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        {
                            log.LogDebug("[Cosium].[Manufacturer].[GetFromDatabaseByExternalCode] SQL command executed");
                            if (reader.HasRows)
                            {
                                DataTable dt = new();
                                dt.Load(reader);
                                log.LogDebug("[Cosium].[Manufacturer].[GetFromDatabaseByExternalCode] SQL reader has {rowCount} rows", dt.Rows.Count);

                                foreach (DataRow dr in dt.Rows)
                                {
                                    Manufacturer newManufacturer = new(dr);
                                    manufacturerList.Add(newManufacturer);
                                }
                                log.LogDebug("[Cosium].[Manufacturer].[GetFromDatabaseByExternalCode] Procedure completed");
                                return manufacturerList;
                            }
                            else
                            {
                                log.LogTrace("[Cosium].[Manufacturer].[GetFromDatabaseByExternalCode] No new vendors were retrieved from the database.");
                            }
                        }
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "[Cosium].[Vendor].[GetFromDatabaseByExternalCode] Attempt to fetch records from the database generated an exception:\r\n{object}", ex.Message);
                }
            }
            return manufacturerList;
        }


        public static async Task PostAsync(List<Manufacturer?> manufacturers, ILogger log)
        {
            log.LogDebug("[Cosium].[Manufacturer].[PostAsync] Procedure started");
            HttpClient client = Common.GetHttpClient();

            try
            {
                foreach (var manufacturer in manufacturers)
                {
                    if (manufacturer != null)
                    {
                        log.LogTrace("[Cosium].[Manufacturer].[PostAsync] Handling record:\r\n{object}", JsonSerializer.Serialize(manufacturer, Common.GetJsonOptions()));

                        var content = new StringContent(JsonSerializer.Serialize(manufacturer, Common.GetJsonOptions()), Encoding.UTF8, "application/json");
                        var request = new HttpRequestMessage(HttpMethod.Post, "manufacturers")
                        {
                            Content = content
                        };

                        log.LogDebug("[Cosium].[Manufacturer].[PostAsync] Sending manufacturer: {manufacturerNo} to API", manufacturer.Code);
                        HttpResponseMessage response = await client.SendAsync(request);
                        var responseBody = await response.Content.ReadAsStringAsync();
                        log.LogTrace("[Cosium].[Manufacturer].[PostAsync] API response:\r\n{status}\r\n{responseBody}",
                                                   response.StatusCode.ToString() + " - " + response.RequestMessage, responseBody);
                        if (response.StatusCode == System.Net.HttpStatusCode.OK)
                        {
                            log.LogInformation("[Cosium].[Manufacturer].[PostAsync] Successfully updated manufacturer: {manufacturerNo}", manufacturer.Code);
                        }
                        else
                        {
                            log.LogError("[Cosium].[Manufacturer].[PostAsync] Attempted to update manufacturer: {manufacturerNo} failed\r\nMessage:{responseBody}",
                                manufacturer.Code, responseBody);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "[Cosium].[Manufacturer].[PostAsync] Encountered an exception:\r\n{object}", ex.Message);
                log.LogDebug("[Cosium].[Manufacturer].[PostAsync] Procedure completed");
            }
        }
    }
}
