﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Text.Json;
using System.Text;
using System.Text.Json.Nodes;
using WSA.Retail.Integration.Model;

namespace WSA.Retail.Integration.Cosium
{
    public class SalesInvoice:Model.SalesInvoice
    {
        public SalesInvoice() { }

        public SalesInvoice(DataRow row) 
        {
            string? externalSystemCode = Environment.GetEnvironmentVariable("ExternalSystemCode");
            ArgumentNullException.ThrowIfNull(externalSystemCode);

            string? requestBody = row.IsNull("JSON") ? null : (string?)row["JSON"];
            if (requestBody != null)
            {
                JsonNode? json = JsonNode.Parse(requestBody);
                ArgumentNullException.ThrowIfNull(json);

                Id = (Guid?)json.AsObject()["id"];
                ExternalSystemCode = externalSystemCode;
                DocumentNumber = (string?)json.AsObject()["documentNumber"];
                AlternateNumber = (string?)json.AsObject()["alternateNumber"];
                ExternalReference = (string?)json.AsObject()["externalReference"];
                SalesOrder = new()
                {
                    Id = (Guid?)json.AsObject()["salesOrder"]?["id"],
                    DocumentNumber = (string?)json.AsObject()["salesOrder"]?["documentNumber"],
                    ExternalReference = (string?)json.AsObject()["salesOrder"]?["externalReference"]
                };
                Patient = new()
                {
                    Id = (Guid?)json.AsObject()["patient"]?["id"],
                    Code = (string?)json.AsObject()["patient"]?["code"],
                    ExternalCode = (string?)json.AsObject()["patient"]?["externalCode"],
                    Name = (string?)json.AsObject()["patient"]?["name"]
                };

                Clinic = new()
                {
                    Id = (Guid?)json.AsObject()["clinic"]?["id"],
                    Code = (string?)json.AsObject()["clinic"]?["code"],
                    ExternalCode = (string?)json.AsObject()["clinic"]?["externalCode"],
                    Name = (string?)json.AsObject()["clinic"]?["name"]
                };

                DocumentDate = DateOnly.FromDateTime((DateTime?)json.AsObject()["documentDate"] ?? DateTime.MinValue);

                SalesInvoiceLines = [];
                JsonArray? jsonArray = json.AsObject()["salesInvoiceLines"]?.AsArray();
                if (jsonArray != null)
                {
                    foreach (var item in jsonArray)
                    {
                        if (item != null)
                        {
                            var thisSalesInvoiceLine = new Model.SalesInvoiceLine()
                            {
                                Id = (Guid?)json.AsObject()["id"],
                                Sequence = (int?)item.AsObject()["sequence"],
                                SalesOrderLine = new()
                                {
                                    Sequence = (int?)item.AsObject()["salesOrderLine"]?["sequence"]
                                },
                                Description = (string?)item.AsObject()["description"],
                                Quantity = (decimal?)item.AsObject()["quantity"],
                                SerialNumber = (string?)item.AsObject()["serialNumber"],
                                UnitPrice = (decimal?)item.AsObject()["unitPrice"],
                                GrossAmount = (decimal?)item.AsObject()["grossAmount"],
                                DiscountAmount = (decimal?)item.AsObject()["discountAmount"],
                                AmountExclTax = (decimal?)item.AsObject()["amountExclTax"],
                                TaxAmount = (decimal?)item.AsObject()["taxAmount"],
                                AmountInclTax = (decimal?)item.AsObject()["amountInclTax"],
                                Product = new()
                                {
                                    Id = (Guid?)item.AsObject()["product"]?["id"],
                                    Code = (string?)item.AsObject()["product"]?["code"],
                                    ExternalCode = (string?)item.AsObject()["product"]?["externalCode"],
                                    Name = (string?)item.AsObject()["product"]?["name"]
                                }
                            };
                            SalesInvoiceLines.Add(thisSalesInvoiceLine);
                        }
                    }
                }
            }
        }

        private static HttpClient? _client;

        public static async Task ProcessNewRecordsAsync(ILogger log)
        {
            var scope = InitScope(nameof(ProcessNewRecordsAsync));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(ProcessNewRecordsAsync));

            List<SalesInvoice?> invoices = await GetFromDatabase(log);
            log.BeginScope(scope);
            log.LogInformation("Fetched {recordCount} sales invoices from Cosium database", invoices?.Count);
            if (invoices?.Count > 0)
            {
                await PostListAsync(invoices, log);
            }
            else
            {
                log.BeginScope(scope);
                log.LogInformation("No new sales invoices are avalable in the Cosium database.");
            }
        }

        private static async Task<List<SalesInvoice?>> GetFromDatabase(ILogger log)
        {
            var scope = InitScope(nameof(GetFromDatabase));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(GetFromDatabase));

            List<SalesInvoice?> salesInvoiceList = [];

            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);

            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "SELECT * FROM cosium.SalesInvoices WHERE IntegrationRequired = 1";
                         using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        {
                            if (reader.HasRows)
                            {
                                DataTable dt = new();
                                dt.Load(reader);
                                log.LogInformation("SQL reader returned {rowCount} rows.", dt.Rows.Count);

                                foreach (DataRow dr in dt.Rows)
                                {
                                    SalesInvoice newInvoice = new(dr);
                                    salesInvoiceList.Add(newInvoice);
                                }
                                return salesInvoiceList;
                            }
                            else
                            {
                                log.LogInformation("No new salesInvoices were retrieved from the database.");
                            }
                        }
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    log.BeginScope(scope);
                    log.LogError(ex, "Attempt to fetch records from the database generated an exception:\r\n{object}", ex.Message);
                }
            }
            return salesInvoiceList;
        }

        public static async Task PostListAsync(List<SalesInvoice?> invoices, ILogger log)
        {
            var scope = InitScope(nameof(PostListAsync));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(PostListAsync));
            try
            {
                foreach (var invoice in invoices)
                {
                    if (invoice != null)
                    {
                        await invoice.PostAsync(log);
                    }
                }
            }
            catch (Exception ex)
            {
                log.BeginScope(scope);
                log.LogError(ex, "Encountered an exception:\r\n{object}", ex.Message);
            }
        }

        public async Task<SalesInvoice?> PostAsync(ILogger log)
        {
            var scope = InitScope(nameof(PostAsync));
            AddEntityToScope(ref scope, this);
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(PostAsync));

            _client ??= Common.GetHttpClient();

            try
            {
                if (this != null)
                {
                    if (this.SalesInvoiceLines.Count > 0)
                    {
                        await ValidateExternalReferences(log);

                        var requestBody = JsonSerializer.Serialize(this, Common.GetJsonOptions());
                        var content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                        var request = new HttpRequestMessage(HttpMethod.Post, "salesInvoices")
                        {
                            Content = content
                        };

                        log.LogInformation("Sending salesInvoice: {code} to API\r\n{requestBody}", this.DocumentNumber, requestBody);
                        HttpResponseMessage response = await _client.SendAsync(request);
                        var responseBody = await response.Content.ReadAsStringAsync();
                        log.LogInformation("API response:\r\n{status}\r\n{responseBody}",
                            response.StatusCode.ToString() + " - " + response.RequestMessage, responseBody);

                        if (response.StatusCode == System.Net.HttpStatusCode.OK)
                        {
                            log.LogInformation("Successfully updated salesInvoice: {code}", this.DocumentNumber);
                            UpdateIsIntegrated();
                            return JsonSerializer.Deserialize<SalesInvoice?>(responseBody, Common.GetJsonOptions());
                        }
                        else
                        {
                            log.LogError("Attempt to update salesInvoice: {code} failed\r\nMessage:{responseBody}",
                                this.DocumentNumber, responseBody);
                        }
                    }
                    else
                    {
                        UpdateIsIntegrated();
                    }
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Encountered an exception:\r\n{object}", ex.Message);
            }
            return null;
        }

        private bool UpdateIsIntegrated()
        {
            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);
            ArgumentNullException.ThrowIfNull(this.ExternalReference);

            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "UPDATE cosium.SalesInvoices SET IntegrationRequired = 0, IntegrationDate = sysutcdatetime() WHERE Id = @id";
                        cmd.Parameters.AddWithValue("@id", this.ExternalReference);
                        cmd.ExecuteNonQuery();
                        conn.Close();
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            }
        }

        public async Task ValidateExternalReferences(ILogger log)
        {
            await Cosium.Clinic.ValidateExternalReference(Clinic);
            await Cosium.Patient.ValidateExternalReference(Patient);
            await ValidateProducts(log);
        }

        public async Task ValidateProducts(ILogger log)
        {
            if (SalesInvoiceLines != null)
            {
                Dictionary<string, Model.ExternalReference> products = [];
                foreach (var line in SalesInvoiceLines) 
                { 
                    if (line?.Product?.ExternalCode != null)
                    {
                        if (!products.ContainsKey(line.Product.ExternalCode))
                        {
                            products.Add(line.Product.ExternalCode, line.Product);
                        }
                    }
                }

                foreach (var product in products.Values) 
                {
                    await Cosium.Product.ValidateExternalReference(product, log);
                }
            }
        }

        private static Dictionary<string, object> InitScope(string procedureName)
        {
            return new Dictionary<string, object>() {
                { "appName", "Cosium" },
                { "className", nameof(SalesInvoice) },
                { "procedureName", procedureName }
            };
        }

        private static void AddEntityToScope(ref Dictionary<string, object> scope, SalesInvoice salesInvoice)
        {
            scope["entityName"] = "SalesInvoice";
            scope["entityCode"] = salesInvoice.DocumentNumber ?? "";
        }
    }
}
