﻿using System.Text.Json.Serialization;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Microsoft.Extensions.Logging;
using WSA.Retail.Integration.DbModel;

namespace WSA.Retail.Integration.Cosium
{
    internal static class Common
    {
        private static ILogger? _logger;

        private static CosiumContext? _context;

        private static string? _externalSystemCode;

        private static string? _sqlConnectionString;

        public static void SetLogger(ILogger logger) { _logger = logger; }

        public static ILogger Logger()
        {
            ArgumentNullException.ThrowIfNull(_logger);
            return _logger;
        }

        public static void SetContext(CosiumContext context) { _context = context; }

        public static CosiumContext Context()
        {
            ArgumentNullException.ThrowIfNull(_context);
            return _context;
        }

        internal static string ExternalSystemCode()
        {
            if (_externalSystemCode == null)
            {
                _externalSystemCode = Environment.GetEnvironmentVariable("ExternalSystemCode");
                ArgumentNullException.ThrowIfNull(_externalSystemCode);
            }
            return _externalSystemCode;
        }

        internal static string SqlConnectionString()
        {
            if (_sqlConnectionString == null)
            {
                _sqlConnectionString = Environment.GetEnvironmentVariable("SqlConnectionString");
                ArgumentNullException.ThrowIfNull(_sqlConnectionString);
            }
            return _sqlConnectionString;
        }

        internal static JsonSerializerOptions GetJsonOptions()
        {
            return new()
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                PropertyNameCaseInsensitive = true,
                NumberHandling = JsonNumberHandling.AllowReadingFromString,
                WriteIndented = true
            };
        }

        public static HttpClient GetHttpClient()
        {
            var apiBaseUrl = Environment.GetEnvironmentVariable("ApiBaseUrl");
            ArgumentNullException.ThrowIfNull(apiBaseUrl);

            var subscriptionKey = Environment.GetEnvironmentVariable("SubscriptionKey");
            ArgumentNullException.ThrowIfNull(subscriptionKey);

            var externalSystemCode = Environment.GetEnvironmentVariable("ExternalSystemCode");
            ArgumentNullException.ThrowIfNull(externalSystemCode);

            var client = new HttpClient
            {
                BaseAddress = new Uri(apiBaseUrl.TrimEnd('/') + '/')
            };
            client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", subscriptionKey);
            client.DefaultRequestHeaders.Add("X-ExternalSystemCode", externalSystemCode);
            return client;
        }

        public static string? GetQueryValue(IQueryCollection query, string name)
        {
            string? returnValue = null;
            if (query.TryGetValue(name, out StringValues values))
            {
                returnValue = values.First();
            }
            return returnValue;
        }

        internal static Dictionary<string, object> InitScope(string className, string procedureName)
        {
            return new Dictionary<string, object>() {
                { "appName", "Cosium" },
                { "className", className },
                { "procedureName", procedureName }
            };
        }
    }
}

