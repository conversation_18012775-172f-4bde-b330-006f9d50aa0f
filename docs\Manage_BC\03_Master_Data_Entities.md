# Master Data Entities

## 📦 Product Entities

### Product
**Source**: `integration_solution/wsa-retail-integration-model/Models/Products/Product.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Products/ProductEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Products/ProductEntityConfiguration.cs`
**Table**: `Product` (Schema: `dbo`)

Core product information for inventory items and services in the Manage-BC integration.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique product identifier |
| `Code` | string | ✅ | 50 | - | Unique, Not Null | Product code for identification |
| `Name` | string? | ❌ | 255 | - | - | Product display name |
| `AlternateCode` | string? | ❌ | 50 | - | - | Alternative product code |
| `PimProductId` | string? | ❌ | 20 | - | - | PIM system product identifier |
| `CategoryId` | Guid? | ❌ | - | - | FK to Category | Product category reference |
| `VendorId` | Guid? | ❌ | - | - | FK to Vendor | Primary vendor reference |
| `ManufacturerId` | Guid? | ❌ | - | - | FK to Manufacturer | Product manufacturer |
| `ColorId` | Guid? | ❌ | - | - | FK to Color | Color variant reference |
| `TaxGroupId` | Guid? | ❌ | - | - | FK to TaxGroup | Tax classification |
| `ProductModelId` | Guid? | ❌ | - | - | FK to ProductModel | Product model reference |
| `BatteryId` | Guid? | ❌ | - | - | FK to Battery | Battery specification |
| `VendorItemNo` | string? | ❌ | 50 | - | - | Vendor's item number |
| `GTIN` | string? | ❌ | 50 | - | Valid GTIN format | Global Trade Item Number |
| `ImageUrl` | string? | ❌ | 500 | - | Valid URL format | Product image URL |
| `IsSerialized` | bool? | ❌ | - | - | - | Requires serial number tracking |
| `IsInventory` | bool? | ❌ | - | - | - | Inventory-tracked item |
| `IsTaxable` | bool? | ❌ | - | - | - | Subject to tax calculations |
| `UnitCost` | decimal? | ❌ | - | - | >= 0 | Standard unit cost |
| `ExternalSystemCode` | string? | ❌ | 20 | - | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | - | External system product ID |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique product identifier |
| `Code` | string | ✅ | 50 | Unique | Business product code |
| `Name` | string? | ❌ | 255 | - | Product display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative product code |
| `PimProductId` | string? | ❌ | 50 | - | PIM system product ID |
| `CategoryId` | Guid? | ❌ | - | FK to Category | Product category reference |
| `VendorId` | Guid? | ❌ | - | FK to Vendor | Primary vendor reference |
| `ManufacturerId` | Guid? | ❌ | - | FK to Manufacturer | Manufacturer reference |
| `ColorId` | Guid? | ❌ | - | FK to Color | Color variant reference |
| `TaxGroupId` | Guid? | ❌ | - | FK to TaxGroup | Tax classification |
| `ProductModelId` | Guid? | ❌ | - | FK to ProductModel | Product model reference |
| `BatteryId` | Guid? | ❌ | - | FK to Battery | Battery specification |
| `VendorItemNo` | string? | ❌ | 50 | - | Vendor's item number |
| `GTIN` | string? | ❌ | 50 | - | Global Trade Item Number |
| `ImageUrl` | string? | ❌ | 500 | Valid URL | Product image URL |
| `IsSerialized` | bool? | ❌ | - | - | Requires serial number tracking |
| `IsInventory` | bool? | ❌ | - | - | Inventory-tracked item |
| `UnitCost` | decimal? | ❌ | - | >= 0 | Standard unit cost |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system product ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Products must have either a `Code` or `ExternalCode`
- Serialized products (`IsSerialized = true`) must be inventory items (`IsInventory = true`)
- `UnitCost` must be non-negative when specified
- `GTIN` must follow valid GTIN format when provided
- `Code` must be unique across all products
- `ImageUrl` must be a valid URL format when provided

#### Database Configuration
```csharp
builder.ToTable("Product", "dbo");
builder.HasKey(p => p.Id);
builder.ConfigureMasterDataFields();
builder.ConfigureAuditInfoFields();

// Specific property configurations
builder.Property(p => p.PimProductId)
    .HasColumnName("PimProductId")
    .HasColumnType("nvarchar(20)")
    .HasMaxLength(20);

builder.Property(p => p.VendorItemNo)
    .HasColumnName("VendorItemNo")
    .HasColumnType("nvarchar(50)")
    .HasMaxLength(50);

builder.Property(p => p.GTIN)
    .HasColumnName("GTIN")
    .HasColumnType("nvarchar(50)")
    .HasMaxLength(50);

builder.Property(p => p.ImageUrl)
    .HasColumnName("ImageUrl")
    .HasColumnType("nvarchar(500)")
    .HasMaxLength(500);
```

#### Relationships
- **Category**: Many-to-One via `CategoryId`
- **Vendor**: Many-to-One via `VendorId`
- **Manufacturer**: Many-to-One via `ManufacturerId`
- **Color**: Many-to-One via `ColorId`
- **TaxGroup**: Many-to-One via `TaxGroupId`
- **ProductModel**: Many-to-One via `ProductModelId`
- **Battery**: Many-to-One via `BatteryId`

---

## 🏥 Clinic Entities

### Clinic
**Source**: `integration_solution/wsa-retail-integration-model/Models/Clinics/Clinic.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Clinics/ClinicEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Clinics/ClinicEntityConfiguration.cs`
**Table**: `Clinic` (Schema: `dbo`)

Healthcare facility information for managing clinic locations and contact details.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique clinic identifier |
| `Code` | string | ✅ | 50 | - | Unique, Not Null | Clinic code for identification |
| `Name` | string? | ❌ | 255 | - | - | Clinic display name |
| `AlternateCode` | string? | ❌ | 50 | - | - | Alternative clinic code |
| `CompanyId` | Guid? | ❌ | - | - | FK to Company | Parent company reference |
| `Address` | string? | ❌ | 100 | - | - | Primary address line |
| `Address2` | string? | ❌ | 100 | - | - | Secondary address line |
| `City` | string? | ❌ | 50 | - | - | City name |
| `Region` | string? | ❌ | 50 | - | - | State/Province/Region |
| `Country` | string? | ❌ | 50 | - | - | Country name |
| `PostalCode` | string? | ❌ | 20 | - | - | ZIP/Postal code |
| `Phone` | string? | ❌ | 20 | - | - | Primary phone number |
| `Email` | string? | ❌ | 100 | - | Valid email format | Primary email address |
| `ExternalSystemCode` | string? | ❌ | 20 | - | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | - | External system clinic ID |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `Code` must be unique across all clinics
- `Email` must be valid email format when provided
- Country codes are validated against `InventoryCountry` table
- Phone numbers should follow standard formatting

#### Database Configuration
```csharp
builder.ToTable("Clinic");
builder.HasKey(x => x.Id);
builder.ConfigureMasterDataFields();
builder.ConfigureAddressFields();
builder.ConfigureContactFields();
builder.ConfigureAuditInfoFields();

// Company relationship
builder.Property(x => x.CompanyId)
    .HasColumnName("CompanyId")
    .HasColumnType("uniqueidentifier")
    .IsRequired(false);

builder.HasOne(x => x.CompanyEntity)
    .WithMany()
    .HasForeignKey(x => x.CompanyId);
```

#### Relationships
- **Company**: Many-to-One via `CompanyId` (optional)

---

## 👤 Patient Entities

### Patient
**Source**: `integration_solution/wsa-retail-integration-model/Models/Patients/Patient.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Patients/PatientEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Patients/PatientEntityConfiguration.cs`
**Table**: `Patient` (Schema: `dbo`)

Customer/patient records for managing patient information and contact details.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique patient identifier |
| `Code` | string | ✅ | 50 | - | Unique, Not Null | Patient code/number |
| `Name` | string? | ❌ | 255 | - | - | Patient full name |
| `AlternateCode` | string? | ❌ | 50 | - | - | Alternative patient code |
| `IdentificationNumber` | string? | ❌ | 20 | - | - | Government ID or SSN |
| `Address` | string? | ❌ | 100 | - | - | Primary address line |
| `Address2` | string? | ❌ | 100 | - | - | Secondary address line |
| `City` | string? | ❌ | 50 | - | - | City name |
| `Region` | string? | ❌ | 50 | - | - | State/Province/Region |
| `Country` | string? | ❌ | 50 | - | - | Country name |
| `PostalCode` | string? | ❌ | 20 | - | - | ZIP/Postal code |
| `Phone` | string? | ❌ | 20 | - | - | Primary phone number |
| `Email` | string? | ❌ | 100 | - | Valid email format | Primary email address |
| `ExternalSystemCode` | string? | ❌ | 20 | - | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | - | External system patient ID |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `Code` must be unique across all patients
- `Email` must be valid email format when provided
- `IdentificationNumber` should be unique when provided
- Patient data is subject to privacy regulations (HIPAA, GDPR)

#### Database Configuration
```csharp
builder.ToTable("Patient");
builder.HasKey(x => x.Id);
builder.ConfigureMasterDataFields();
builder.ConfigureAddressFields();
builder.ConfigureContactFields();
builder.ConfigureAuditInfoFields();

builder.Property(x => x.IdentificationNumber)
    .HasColumnName("IdentificationNumber")
    .HasColumnType("nvarchar(20)")
    .IsRequired(false);
```

---

## 🏭 Manufacturer Entities

### Manufacturer
**Source**: `integration_solution/wsa-retail-integration-model/Models/Manufacturers/Manufacturer.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Manufacturers/ManufacturerEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Manufacturers/ManufacturerEntityConfiguration.cs`
**Table**: `Manufacturer` (Schema: `dbo`)

Product manufacturer information for tracking product origins and supplier relationships.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique manufacturer identifier |
| `Code` | string | ✅ | 50 | - | Unique, Not Null | Manufacturer code |
| `Name` | string? | ❌ | 255 | - | - | Manufacturer display name |
| `AlternateCode` | string? | ❌ | 50 | - | - | Alternative manufacturer code |
| `ExternalSystemCode` | string? | ❌ | 20 | - | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | - | External system manufacturer ID |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `Code` must be unique across all manufacturers
- Manufacturer names should be standardized for consistency

#### Database Configuration
```csharp
builder.ToTable("Manufacturer");
builder.HasKey(x => x.Id);
builder.ConfigureMasterDataFields();
builder.ConfigureAuditInfoFields();
```

#### Relationships
- **Products**: One-to-Many (Manufacturer can have multiple products)

---

## 📱 Product Model Entities

### ProductModel
**Source**: `integration_solution/wsa-retail-integration-model/Models/ProductModels/ProductModel.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/ProductModels/ProductModelEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/ProductModels/ProductModelEntityConfiguration.cs`
**Table**: `ProductModel` (Schema: `dbo`)

Product model information for categorizing products by model specifications.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique product model identifier |
| `Code` | string | ✅ | 50 | - | Unique, Not Null | Product model code |
| `Name` | string? | ❌ | 255 | - | - | Product model display name |
| `AlternateCode` | string? | ❌ | 50 | - | - | Alternative model code |
| `ExternalSystemCode` | string? | ❌ | 20 | - | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | - | External system model ID |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `Code` must be unique across all product models
- Product models are used for grouping related products

#### Database Configuration
```csharp
builder.ToTable("ProductModel", "dbo");
builder.HasKey(m => m.Id);
builder.ConfigureMasterDataFields();
builder.ConfigureAuditInfoFields();
```

#### Relationships
- **Products**: One-to-Many (ProductModel can have multiple products)

---

## 🗂️ Configuration Entities

### CategoryMapping
**Source**: `integration_solution/retail-integration-manage/Manage/Models/CategoryMappings/CategoryMappingEntity.cs`
**Configuration**: `integration_solution/retail-integration-manage/Manage/Models/CategoryMappings/CategoryMappingConfiguration.cs`
**Table**: `CategoryMapping` (Schema: `manage`)

Category mapping entities for hearing aid business logic and product categorization.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique mapping identifier |
| `CategoryId` | Guid | ✅ | - | - | Unique, FK to Category | Category reference |
| `IsHearingAid` | bool | ✅ | - | `false` | Not Null | Hearing aid business logic flag |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Last modification timestamp |

#### Business Rules
- `CategoryId` must be unique (one mapping per category)
- `IsHearingAid` flag drives specific business logic for hearing aid products

#### Database Configuration
```csharp
builder.ToTable("CategoryMapping", "manage");
builder.HasKey(e => e.Id);

builder.Property(e => e.Id)
    .HasDefaultValueSql("NEWID()")
    .ValueGeneratedOnAdd();

builder.Property(e => e.IsHearingAid)
    .HasDefaultValue(false)
    .IsRequired();

builder.Property(e => e.CreatedOn)
    .HasDefaultValueSql("sysutcdatetime()")
    .ValueGeneratedOnAdd()
    .IsRequired();

builder.Property(e => e.ModifiedOn)
    .HasDefaultValueSql("sysutcdatetime()")
    .ValueGeneratedOnAdd()
    .IsRequired();

// Unique index on CategoryId
builder.HasIndex(e => e.CategoryId)
    .IsUnique()
    .HasDatabaseName("IX_CategoryMapping_CategoryId");
```

#### Indexes
- **IX_CategoryMapping_CategoryId**: Unique index on `CategoryId`

---

### InventoryCountry
**Source**: `integration_solution/retail-integration-manage/Manage/Models/InventoryCountries/InventoryCountryEntity.cs`
**Configuration**: `integration_solution/retail-integration-manage/Manage/Models/InventoryCountries/InventoryCountryConfiguration.cs`
**Table**: `InventoryCountry` (Schema: `manage`)

Geographic configuration for inventory management and country-specific business rules.

#### Properties

| Property | Type | Required | Max Length | Default | Constraints | Description |
|----------|------|----------|------------|---------|-------------|-------------|
| `Id` | Guid | ✅ | - | `NEWID()` | Primary Key | Unique country identifier |
| `Name` | string | ✅ | 100 | - | Unique, Not Null | Country full name |
| `Code` | string | ✅ | 20 | - | Not Null | Country code (ISO format) |
| `CreatedOn` | DateTime | ✅ | - | `sysutcdatetime()` | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | Auto-updated | UTC | Last modification timestamp |

#### Business Rules
- `Name` must be unique across all countries
- `Code` should follow ISO country code standards
- Used for validating and standardizing country information in clinic and patient records

#### Database Configuration
```csharp
builder.ToTable("InventoryCountry", "manage");
builder.HasKey(e => e.Id);

builder.Property(e => e.Name)
    .HasColumnType("nvarchar(100)")
    .HasMaxLength(100)
    .IsRequired();

builder.Property(e => e.Code)
    .HasColumnType("nvarchar(20)")
    .HasMaxLength(20)
    .IsRequired();

// Unique index on Name
builder.HasIndex(e => e.Name)
    .IsUnique()
    .HasDatabaseName("IX_InventoryCountry_Name");
```

#### Indexes
- **IX_InventoryCountry_Name**: Unique index on `Name`

### Category
**Source**: `integration_solution/wsa-retail-integration-model/Models/Categories/Category.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Categories/CategoryEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Categories/CategoryEntityConfiguration.cs`

Product categorization and hierarchy management.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique category identifier |
| `Code` | string | ✅ | 50 | Unique | Business category code |
| `Name` | string? | ❌ | 255 | - | Category display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative category code |
| `ParentId` | Guid? | ❌ | - | FK to Category | Parent category (hierarchy) |
| `IsHearingAid` | bool? | ❌ | - | - | Hearing aid category flag |
| `IsInventory` | bool? | ❌ | - | - | Inventory category flag |
| `IsSerialized` | bool? | ❌ | - | - | Serialization requirement flag |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system category ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Categories can form hierarchical structures via `ParentId`
- Circular references in hierarchy are not allowed
- Hearing aid categories require special handling in BC integration

### Color
**Source**: `integration_solution/wsa-retail-integration-model/Models/Colors/Color.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Colors/ColorEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Colors/ColorEntityConfiguration.cs`

Color variants for products.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique color identifier |
| `Code` | string | ✅ | 50 | Unique | Business color code |
| `Name` | string? | ❌ | 255 | - | Color display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative color code |
| `HexCode` | string? | ❌ | 7 | Valid hex | HTML color code (#RRGGBB) |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system color ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- `HexCode` must be valid HTML color format when provided
- Colors are primarily managed in Manage system, not synchronized to BC

## 🏢 Organization Entities

### Clinic
**Source**: `integration_solution/wsa-retail-integration-model/Models/Clinics/Clinic.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Clinics/ClinicEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Clinics/ClinicEntityConfiguration.cs`

Healthcare facility information.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique clinic identifier |
| `Code` | string | ✅ | 50 | Unique | Business clinic code |
| `Name` | string? | ❌ | 255 | - | Clinic display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative clinic code |
| `CompanyId` | Guid? | ❌ | - | FK to Company | Parent company reference |
| `Address` | string? | ❌ | 100 | - | Primary address line |
| `Address2` | string? | ❌ | 100 | - | Secondary address line |
| `City` | string? | ❌ | 50 | - | City name |
| `Region` | string? | ❌ | 50 | - | State/Province/Region |
| `Country` | string? | ❌ | 50 | - | Country name or code |
| `PostalCode` | string? | ❌ | 20 | - | ZIP/Postal code |
| `Phone` | string? | ❌ | 20 | - | Primary phone number |
| `Email` | string? | ❌ | 100 | Valid email | Primary email address |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system clinic ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Clinics are mapped to Responsibility Centers in BC
- Email must be valid format when provided
- Country codes are validated against inventory country lookup

### Company
**Source**: `integration_solution/wsa-retail-integration-model/Models/Companies/Company.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Companies/CompanyEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Companies/CompanyEntityConfiguration.cs`

Company/organization information.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique company identifier |
| `Code` | string | ✅ | 50 | Unique | Business company code |
| `Name` | string? | ❌ | 255 | - | Company display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative company code |
| `Address` | string? | ❌ | 100 | - | Primary address line |
| `Address2` | string? | ❌ | 100 | - | Secondary address line |
| `City` | string? | ❌ | 50 | - | City name |
| `Region` | string? | ❌ | 50 | - | State/Province/Region |
| `Country` | string? | ❌ | 50 | - | Country name or code |
| `PostalCode` | string? | ❌ | 20 | - | ZIP/Postal code |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system company ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

## 👥 People Entities

### Patient
**Source**: `integration_solution/wsa-retail-integration-model/Models/Patients/Patient.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Patients/PatientEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Patients/PatientEntityConfiguration.cs`

Patient/customer information.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique patient identifier |
| `Code` | string | ✅ | 50 | Unique | Business patient code |
| `Name` | string? | ❌ | 255 | - | Patient full name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative patient code |
| `IdentificationNumber` | string? | ❌ | 20 | - | Government ID or SSN |
| `Address` | string? | ❌ | 100 | - | Primary address line |
| `Address2` | string? | ❌ | 100 | - | Secondary address line |
| `City` | string? | ❌ | 50 | - | City name |
| `Region` | string? | ❌ | 50 | - | State/Province/Region |
| `Country` | string? | ❌ | 50 | - | Country name or code |
| `PostalCode` | string? | ❌ | 20 | - | ZIP/Postal code |
| `Phone` | string? | ❌ | 20 | - | Primary phone number |
| `Email` | string? | ❌ | 100 | Valid email | Primary email address |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system patient ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Patients are mapped to Customers in BC
- Email must be valid format when provided
- `IdentificationNumber` may be subject to privacy regulations (GDPR, HIPAA)

## 🏭 Vendor and Manufacturer Entities

### Vendor
**Source**: `integration_solution/wsa-retail-integration-model/Models/Vendors/Vendor.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Vendors/VendorEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Vendors/VendorEntityConfiguration.cs`

Supplier and vendor information.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique vendor identifier |
| `Code` | string | ✅ | 50 | Unique | Business vendor code |
| `Name` | string? | ❌ | 255 | - | Vendor display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative vendor code |
| `Address` | string? | ❌ | 100 | - | Primary address line |
| `Address2` | string? | ❌ | 100 | - | Secondary address line |
| `City` | string? | ❌ | 50 | - | City name |
| `Region` | string? | ❌ | 50 | - | State/Province/Region |
| `Country` | string? | ❌ | 50 | - | Country name or code |
| `PostalCode` | string? | ❌ | 20 | - | ZIP/Postal code |
| `Phone` | string? | ❌ | 20 | - | Primary phone number |
| `Email` | string? | ❌ | 100 | Valid email | Primary email address |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system vendor ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Vendors are synchronized bidirectionally between Manage and BC
- Email must be valid format when provided
- Vendor codes must be unique across the system

### Manufacturer
**Source**: `integration_solution/wsa-retail-integration-model/Models/Manufacturers/Manufacturer.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Manufacturers/ManufacturerEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Manufacturers/ManufacturerEntityConfiguration.cs`

Product manufacturer information.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique manufacturer identifier |
| `Code` | string | ✅ | 50 | Unique | Business manufacturer code |
| `Name` | string? | ❌ | 255 | - | Manufacturer display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative manufacturer code |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system manufacturer ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Manufacturers are synchronized bidirectionally between Manage and BC
- Used for product attribution and reporting

## 🔋 Product Specification Entities

### Battery
**Source**: `integration_solution/wsa-retail-integration-model/Models/Batteries/Battery.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Batteries/BatteryEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Batteries/BatteryEntityConfiguration.cs`

Battery specifications for hearing aids and devices.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique battery identifier |
| `Code` | string | ✅ | 50 | Unique | Business battery code |
| `Name` | string? | ❌ | 255 | - | Battery display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative battery code |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system battery ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Batteries are primarily managed in Manage system
- Used for hearing aid product specifications
- Not synchronized to BC system

### ProductModel
**Source**: `integration_solution/wsa-retail-integration-model/Models/ProductModels/ProductModel.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/ProductModels/ProductModelEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/ProductModels/ProductModelEntityConfiguration.cs`

Product model specifications and variants.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique product model identifier |
| `Code` | string | ✅ | 50 | Unique | Business product model code |
| `Name` | string? | ❌ | 255 | - | Product model display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative product model code |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system product model ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Product models define specific variants or configurations
- Used for detailed product categorization

## 💰 Financial Entities

### TaxGroup
**Source**: `integration_solution/wsa-retail-integration-model/Models/TaxGroups/TaxGroup.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/TaxGroups/TaxGroupEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/TaxGroups/TaxGroupEntityConfiguration.cs`

Tax classification and rates.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique tax group identifier |
| `Code` | string | ✅ | 50 | Unique | Business tax group code |
| `Name` | string? | ❌ | 255 | - | Tax group display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative tax group code |
| `Rate` | decimal? | ❌ | - | 0-100 | Tax rate percentage |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system tax group ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Tax rates are expressed as percentages (0-100)
- Synchronized bidirectionally between Manage and BC
- Critical for accurate financial calculations

### PaymentMethod
**Source**: `integration_solution/wsa-retail-integration-model/Models/PaymentMethods/PaymentMethod.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/PaymentMethods/PaymentMethodEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/PaymentMethods/PaymentMethodEntityConfiguration.cs`

Payment method definitions.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique payment method identifier |
| `Code` | string | ✅ | 50 | Unique | Business payment method code |
| `Name` | string? | ❌ | 255 | - | Payment method display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative payment method code |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system payment method ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Payment methods are synchronized bidirectionally
- Used in sales transactions and payment processing

### Payor
**Source**: `integration_solution/wsa-retail-integration-model/Models/Payors/Payor.cs`
**Entity**: `integration_solution/wsa-retail-integration-model/Models/Payors/PayorEntity.cs`
**Configuration**: `integration_solution/wsa-retail-integration-model/Models/Payors/PayorEntityConfiguration.cs`

Insurance companies and payment organizations.

#### Properties

| Property | Type | Required | Max Length | Constraints | Description |
|----------|------|----------|------------|-------------|-------------|
| `Id` | Guid | ✅ | - | Primary Key | Unique payor identifier |
| `Code` | string | ✅ | 50 | Unique | Business payor code |
| `Name` | string? | ❌ | 255 | - | Payor display name |
| `AlternateCode` | string? | ❌ | 50 | - | Alternative payor code |
| `ExternalSystemCode` | string? | ❌ | 20 | - | Source system identifier |
| `ExternalCode` | string? | ❌ | 50 | - | External system payor ID |
| `CreatedOn` | DateTime | ✅ | - | UTC | Creation timestamp |
| `ModifiedOn` | DateTime | ✅ | - | UTC | Last modification timestamp |

#### Business Rules
- Payors represent insurance companies or other payment entities
- Used in claims processing and payment tracking

---

*This completes the master data entities documentation. Each entity follows consistent patterns and includes comprehensive constraint information for integration purposes.*
