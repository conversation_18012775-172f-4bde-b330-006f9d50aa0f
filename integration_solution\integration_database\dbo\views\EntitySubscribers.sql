﻿CREATE VIEW [dbo].[EntitySubscribers]
AS 

SELECT Source.Id,
       ExternalSystem.Code AS ExternalSystemCode,
       Entity.Code AS EntityCode,
       Source.[ModifiedOn],
       t1.JSON

  FROM dbo.EntitySubscriber AS Source

  LEFT JOIN dbo.ExternalSystem
    ON Source.ExternalSystemId = ExternalSystem.Id

  LEFT JOIN dbo.Entity
    ON Source.EntityId = Entity.Id

 OUTER APPLY (SELECT(
              SELECT EntitySubscriber.Id AS 'id',

                     ExternalSystem.Id AS 'externalSystem.id',
                     ExternalSystem.Code AS 'externalSystem.code',
                     ExternalSystem.[Name] AS 'externalSystem.name',

                     Entity.Id AS 'entity.id',
                     Entity.Code AS 'entity.code',
                     Entity.[Name] AS 'entity.name',

                     EntitySubscriber.FromExternalSystem AS 'fromExternalSystem',
                     EntitySubscriber.ToExternalSystem AS 'toExternalSystem'

                FROM dbo.EntitySubscriber

                LEFT JOIN dbo.ExternalSystem
                       ON EntitySubscriber.ExternalSystemId = ExternalSystem.Id

                LEFT JOIN dbo.Entity
                       ON EntitySubscriber.EntityId = Entity.Id

                WHERE EntitySubscriber.Id = Source.Id

                  FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
                      ) AS JSON
                      ) AS t1