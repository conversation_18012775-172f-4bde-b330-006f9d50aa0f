# {{className}} Configuration

## Overview
**File**: `{{filePath}}`  
**Namespace**: `{{namespace}}`  
**Entity**: `{{entityName}}`

## Table Configuration
- **Table**: `{{tableName}}`
- **Schema**: `{{schemaName}}`

## Primary Key
```csharp
{{primaryKeyConfiguration}}
```

## Properties
{{#each properties}}
### {{propertyName}}
- **Column**: `{{columnName}}`
- **Type**: `{{columnType}}`
- **Required**: {{required}}
- **Max Length**: {{maxLength}}
- **Default**: {{defaultValue}}

```csharp
{{configuration}}
```
{{/each}}

## Indexes
{{#each indexes}}
### {{name}}
- **Type**: {{type}}
- **Columns**: {{columns}}
- **Unique**: {{unique}}

```csharp
{{configuration}}
```
{{/each}}

## Relationships
{{#each relationships}}
### {{name}}
- **Type**: {{type}}
- **Foreign Key**: `{{foreignKey}}`
- **Referenced Table**: `{{referencedTable}}`

```csharp
{{configuration}}
```
{{/each}}

## Common Patterns
{{#each commonPatterns}}
### {{name}}
{{description}}
{{/each}}

---
*Generated: {{generatedDate}}*
