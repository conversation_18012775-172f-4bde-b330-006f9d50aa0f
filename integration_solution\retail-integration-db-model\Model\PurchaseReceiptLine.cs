﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model;

public class PurchaseReceiptLine
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("sequence")]
    public int? Sequence { get; set; }

    [JsonPropertyName("purchaseOrderLine")]
    public ExternalDocumentLineReference? PurchaseOrderLine { get; set; }

    [JsonPropertyName("product")]
    public ExternalReference? Product { get; set; }

    [JsonPropertyName("quantity")]
    public decimal? Quantity { get; set; }

    [JsonPropertyName("unitPrice")]
    public decimal? UnitPrice { get; set; }

    [JsonPropertyName("grossAmount")]
    public decimal? GrossAmount { get; set; }

    [JsonPropertyName("discountAmount")]
    public decimal? DiscountAmount { get; set; }

    [JsonPropertyName("amountExclTax")]
    public decimal? AmountExclTax { get; set; }

    [JsonPropertyName("taxAmount")]
    public decimal? TaxAmount { get; set; }

    [JsonPropertyName("amountInclTax")]
    public decimal? AmountInclTax { get; set; }

    [JsonPropertyName("serialNumber")]
    public string? SerialNumber { get; set; }
}

