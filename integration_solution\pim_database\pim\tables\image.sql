﻿CREATE TABLE [pim].[Image]
(
    [Id]                    UNIQUE<PERSON>ENTIFIER CONSTRAINT [DF_Image_Id] DEFAULT NEWID() NOT NULL,
    [ProductId]             UNIQUEIDENTIFIER NOT NULL,
    [BrandId]               UNIQUE<PERSON>ENTIFIER NULL,
    [CDNUrl]                NVARCHAR(250) NULL,
    [IsDefault]             BIT NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_Image_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_Image_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_Image_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_Image_ProductId_BrandId_CDNUrl] ON [pim].[Image]
(
	[ProductId] ASC,
	[BrandId] ASC,
	[CDNUrl] ASC
)
GO