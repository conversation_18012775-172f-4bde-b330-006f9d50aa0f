# {{className}} Entity

## 📋 Overview

**Namespace**: `{{namespace}}`  
**Source File**: `{{filePath}}`  
**Table**: `{{tableName}}`  
**Schema**: `{{schemaName}}`  

{{description}}

## 🏗️ Entity Structure

### Class Definition
```csharp
{{classDefinition}}
```

## 📊 Properties

| Property | Type | Required | Max Length | Default Value | Constraints | Description |
|----------|------|----------|------------|---------------|-------------|-------------|
{{#each properties}}
| `{{name}}` | {{type}} | {{#if required}}✅{{else}}❌{{/if}} | {{maxLength}} | {{defaultValue}} | {{constraints}} | {{description}} |
{{/each}}

## 🔗 Relationships

### Foreign Keys
{{#each foreignKeys}}
- **{{propertyName}}** → `{{referencedTable}}.{{referencedColumn}}`
  - **Relationship**: {{relationshipType}}
  - **Delete Behavior**: {{deleteBehavior}}
  - **Required**: {{#if required}}Yes{{else}}No{{/if}}
{{/each}}

### Navigation Properties
{{#each navigationProperties}}
- **{{name}}** ({{type}})
  - **Relationship**: {{relationshipType}}
  - **Foreign Key**: {{foreignKeyProperty}}
{{/each}}

## 🗂️ Database Configuration

### Table Configuration
```csharp
builder.ToTable("{{tableName}}"{{#if schemaName}}, "{{schemaName}}"{{/if}});
```

### Primary Key
```csharp
builder.HasKey({{primaryKeyExpression}});
```

### Column Configurations
{{#each columnConfigurations}}
```csharp
builder.Property({{propertyExpression}})
    .HasColumnName("{{columnName}}")
    .HasColumnType("{{columnType}}")
    {{#if maxLength}}.HasMaxLength({{maxLength}}){{/if}}
    {{#if required}}.IsRequired(){{else}}.IsRequired(false){{/if}}
    {{#if defaultValue}}.HasDefaultValue({{defaultValue}}){{/if}}
    {{#if defaultValueSql}}.HasDefaultValueSql("{{defaultValueSql}}"){{/if}};
```
{{/each}}

## 📇 Indexes

{{#each indexes}}
### {{name}}
- **Type**: {{#if unique}}Unique{{else}}Non-Unique{{/if}}
- **Columns**: {{columns}}
- **Database Name**: `{{databaseName}}`

```csharp
builder.HasIndex({{indexExpression}})
    {{#if unique}}.IsUnique(){{/if}}
    .HasDatabaseName("{{databaseName}}");
```
{{/each}}

## ✅ Validation Rules

### Data Annotations
{{#each validationAttributes}}
- **{{attributeName}}**: {{description}}
  {{#if parameters}}- Parameters: {{parameters}}{{/if}}
{{/each}}

### Business Rules
{{#each businessRules}}
- {{rule}}
{{/each}}

### Constraints Summary
{{#each constraints}}
- **{{type}}**: {{description}}
  {{#if errorMessage}}- Error Message: "{{errorMessage}}"{{/if}}
{{/each}}

## 🔄 Common Configuration Patterns

{{#if usesMasterDataFields}}
### Master Data Fields
Applied via `ConfigureMasterDataFields()`:
- **Id**: `uniqueidentifier`, Primary Key
- **Code**: `nvarchar(50)`, Required, Unique
- **Name**: `nvarchar(255)`, Optional
- **AlternateCode**: `nvarchar(50)`, Optional
{{/if}}

{{#if usesAddressFields}}
### Address Fields
Applied via `ConfigureAddressFields()`:
- **Address**: `nvarchar(100)`, Optional
- **Address2**: `nvarchar(100)`, Optional
- **City**: `nvarchar(50)`, Optional
- **Region**: `nvarchar(50)`, Optional
- **Country**: `nvarchar(50)`, Optional
- **PostalCode**: `nvarchar(20)`, Optional
{{/if}}

{{#if usesContactFields}}
### Contact Fields
Applied via `ConfigureContactFields()`:
- **Phone**: `nvarchar(20)`, Optional
- **Email**: `nvarchar(100)`, Optional
{{/if}}

{{#if usesAuditFields}}
### Audit Info Fields
Applied via `ConfigureAuditInfoFields()`:
- **CreatedOn**: `datetime2(7)`, Required, Default: `sysutcdatetime()`
- **ModifiedOn**: `datetime2(7)`, Required, Auto-updated
{{/if}}

## 📈 Usage Examples

### Entity Creation
```csharp
var entity = new {{className}}
{
    {{#each requiredProperties}}
    {{name}} = {{exampleValue}},
    {{/each}}
};
```

### Query Examples
```csharp
// Find by Code
var entity = context.{{pluralName}}.FirstOrDefault(x => x.Code == "{{exampleCode}}");

// Include related entities
var entityWithRelations = context.{{pluralName}}
    {{#each navigationProperties}}
    .Include(x => x.{{name}})
    {{/each}}
    .FirstOrDefault(x => x.Id == id);
```

## 🔗 Related Entities

{{#each relatedEntities}}
- [{{name}}]({{link}}) - {{relationship}}
{{/each}}

## 📝 Notes

{{#each notes}}
- {{note}}
{{/each}}

---

*Generated on {{generatedDate}} from source code*  
*Last modified: {{lastModified}}*
