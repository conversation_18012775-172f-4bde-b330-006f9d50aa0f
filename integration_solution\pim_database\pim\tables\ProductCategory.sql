﻿CREATE TABLE [pim].[ProductCategory]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_ProductCategory_id] DEFAULT NEWID() NOT NULL,
    [ProductId]             UNIQUEIDENTIFIER NOT NULL,
    [CategoryId]            UNIQUEIDENTIFIER NOT NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_ProductCategory_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_ProductCategory_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    CONSTRAINT              [PK_ProductCategory_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE UNIQUE INDEX IX_ProductCategory_ProductId
ON [pim].[ProductCategory] ([ProductId], [CategoryId])
GO