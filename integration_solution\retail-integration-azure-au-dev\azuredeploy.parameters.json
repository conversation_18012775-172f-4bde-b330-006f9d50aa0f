{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"collation": {"value": "SQL_Latin1_General_CP1_CI_AS"}, "databaseName": {"value": "au_dev"}, "tier": {"value": "GeneralPurpose"}, "skuName": {"value": "GP_S_Gen5_1"}, "location": {"value": "australiaeast"}, "maxSizeBytes": {"value": ***********}, "sampleName": {"value": ""}, "serverName": {"value": "retail-integration-au-dev"}, "licenseType": {"value": ""}, "readScaleOut": {"value": "Disabled"}, "zoneRedundant": {"value": false}, "minCapacity": {"value": "0.5"}, "autoPauseDelay": {"value": 60}, "numberOfReplicas": {"value": 0}, "enableADS": {"value": false}, "allowAzureIps": {"value": true}, "allowClientIp": {"value": true}, "clientIpValue": {"value": "*************"}, "databaseTags": {"value": {}}, "serverTags": {"value": {}}, "enableVA": {"value": false}, "vaStoragelessEnabled": {"value": false}, "enableSqlLedger": {"value": false}, "enableDigestStorage": {"value": "Disabled"}, "digestStorageOption": {"value": ""}, "digestStorageName": {"value": ""}, "blobStorageContainerName": {"value": ""}, "retentionDays": {"value": ""}, "retentionPolicy": {"value": false}, "digestAccountResourceGroup": {"value": ""}, "isNewDigestLocation": {"value": false}, "digestRegion": {"value": ""}, "storageAccountdigestRegion": {"value": ""}, "isPermissionAssigned": {"value": false}, "sqlLedgerTemplateLink": {"value": "https://sqlazureextension.hosting.portal.azure.net/sqlazureextension/Content/2.1.********/DeploymentTemplates/SqlLedger.json"}, "useVAManagedIdentity": {"value": false}, "enablePrivateEndpoint": {"value": false}, "privateEndpointNestedTemplateId": {"value": "pe-2454c511-6051-4abb-9303-488ddfd4e047"}, "privateEndpointSubscriptionId": {"value": ""}, "privateEndpointResourceGroup": {"value": ""}, "privateEndpointName": {"value": ""}, "privateEndpointLocation": {"value": ""}, "privateEndpointSubnetId": {"value": ""}, "privateLinkServiceName": {"value": ""}, "privateLinkServiceServiceId": {"value": ""}, "privateEndpointVnetSubscriptionId": {"value": ""}, "privateEndpointVnetResourceGroup": {"value": ""}, "privateEndpointVnetName": {"value": ""}, "privateEndpointSubnetName": {"value": ""}, "enablePrivateDnsZone": {"value": true}, "privateLinkPrivateDnsZoneFQDN": {"value": "privatelink.database.windows.net"}, "privateLinkPrivateDnsZoneRG": {"value": ""}, "privateEndpointDnsRecordUniqueId": {"value": "2454c511-6051-4abb-9303-488ddfd4e04a"}, "privateEndpointTemplateLink": {"value": "https://sqlazureextension.hosting.portal.azure.net/sqlazureextension/Content/2.1.********/DeploymentTemplates/PrivateEndpoint.json"}, "privateDnsForPrivateEndpointTemplateLink": {"value": "https://sqlazureextension.hosting.portal.azure.net/sqlazureextension/Content/2.1.********/DeploymentTemplates/PrivateDnsForPrivateEndpoint.json"}, "privateDnsForPrivateEndpointNicTemplateLink": {"value": "https://sqlazureextension.hosting.portal.azure.net/sqlazureextension/Content/2.1.********/DeploymentTemplates/PrivateDnsForPrivateEndpointNic.json"}, "privateDnsForPrivateEndpointIpConfigTemplateLink": {"value": "https://sqlazureextension.hosting.portal.azure.net/sqlazureextension/Content/2.1.********/DeploymentTemplates/PrivateDnsForPrivateEndpointIpConfig.json"}, "clientIpRuleName": {"value": "ClientIp-2024-9-30_7-55-2"}, "minimalTlsVersion": {"value": "1.2"}, "publicNetworkAccess": {"value": "Enabled"}, "connectionType": {"value": "<PERSON><PERSON><PERSON>"}, "requestedBackupStorageRedundancy": {"value": "Local"}, "useFreeLimit": {"value": false}, "freeLimitExhaustionBehavior": {"value": "AutoPause"}, "maintenanceConfigurationId": {"value": "/subscriptions/f4c4fcf2-8505-47ba-bf20-1fbc8590fca0/providers/Microsoft.Maintenance/publicMaintenanceConfigurations/SQL_Default"}, "administrators": {"value": {"administratorType": "activeDirectory", "login": "<EMAIL>", "sid": "dc7ec949-0a9e-4a09-9ab2-b40ff8ed8d60", "tenantId": "1a41b96d-457d-41ac-94ef-22d1901a7556", "azureADOnlyAuthentication": true, "principalType": "User"}}, "identity": {"value": {"type": "None"}}, "primaryUserAssignedIdentityId": {"value": ""}, "federatedClientId": {"value": ""}, "servicePrincipal": {"value": {"type": "None"}}}}