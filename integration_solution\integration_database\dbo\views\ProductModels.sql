﻿CREATE VIEW [dbo].[ProductModels]
AS

  WITH Source AS (
SELECT ProductModel.Id,
       ExternalSystem.Code AS ExternalSystemCode,
       ProductModel.Code,
       Coupling.ExternalRecordId AS ExternalCode,
       ProductModel.[Name],
       ProductModel.CreatedOn,
       ProductModel.ModifiedOn

  FROM dbo.ProductModel

 CROSS JOIN dbo.ExternalSystem

 LEFT JOIN dbo.Coupling
   ON ProductModel.Id = Coupling.RecordId
  AND ExternalSystem.Id = Coupling.ExternalSystemId
      )  

SELECT Source.Id,
       Source.ExternalSystemCode,
       Source.Code,
       Source.ExternalCode,
       Source.[Name],
       Source.[CreatedOn],
       Source.[ModifiedOn],
       (
       SELECT Source.Id AS 'id',
              Source.ExternalSystemCode AS 'externalSystemCode',
              Source.Code AS 'code',
              Source.ExternalCode AS 'externalCode',
              Source.[Name] AS 'name',
              Source.CreatedOn AS 'createdOn',
              Source.ModifiedOn AS 'modifiedOn'

           FOR <PERSON>SON PATH, WITHOUT_ARRAY_WRAPPER
       ) AS JSON
  FROM Source