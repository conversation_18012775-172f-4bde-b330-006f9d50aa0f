﻿CREATE VIEW [dbo].[PurchaseReturns]
AS

SELECT Source.Id,
       Source.DocumentNumber,
       (SELECT TOP 1 ExternalRecordId
          FROM dbo.Coupling
         WHERE Coupling.RecordId = Source.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS ExternalReference,
       Source.DocumentDate,
       ExternalSystem.Id AS ExternalSystemId,
       ExternalSystem.Code AS ExternalSystemCode,
       Source.ModifiedOn,
       t1.JSON

  FROM dbo.PurchaseReturn AS Source

 OUTER APPLY dbo.ExternalSystem

 OUTER APPLY (SELECT(
              SELECT PurchaseReturn.Id AS id,
                     PurchaseReturn.DocumentNumber AS documentNumber,
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = PurchaseReturn.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'externalReference',
                     PurchaseReturn.AlternateNumber AS alternateNumber,

                     Vendor.Id AS 'vendor.id',
                     Vendor.Code AS 'vendor.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Vendor.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'vendor.externalCode',
                     Vendor.[Name] AS 'vendor.name',                     
                     
                     Clinic.Id AS 'clinic.id',
                     Clinic.Code AS 'clinic.code',
                     (SELECT TOP 1 ExternalRecordId 
                        FROM dbo.Coupling 
                       WHERE Coupling.RecordId = Clinic.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'clinic.externalCode',
                     Clinic.[Name] AS 'clinic.name',

                     PurchaseReturn.DocumentDate AS documentDate,
                     (
                            SELECT PurchaseReturnLine.[Sequence] AS [sequence],
                                   Product.Id AS 'product.id',
                                   Product.Code AS 'product.code',
                                   (SELECT TOP 1 ExternalRecordId 
                                      FROM dbo.Coupling 
                                      WHERE Coupling.RecordId = Product.Id AND Coupling.ExternalSystemId = ExternalSystem.Id) AS 'product.externalCode',
                                   Product.[Name] AS 'product.name',
                                   PurchaseReturnLine.[Description] AS [description],
                                   PurchaseReturnLine.[Quantity] AS quantity,
                                   PurchaseReturnLine.[UnitPrice] AS unitPrice,
                                   PurchaseReturnLine.[GrossAmount] AS grossAmount,
                                   PurchaseReturnLine.[DiscountAmount] AS discountAmount,
                                   PurchaseReturnLine.[AmountInclTax] AS amountInclTax,
                                   PurchaseReturnLine.[TaxAmount] AS taxAmount,
                                   PurchaseReturnLine.[AmountExclTax] AS amountExclTax

                              FROM dbo.PurchaseReturnLine

                                   LEFT JOIN dbo.Product
                                          ON PurchaseReturnLine.ProductId = Product.Id

                             WHERE PurchaseReturnLine.PurchaseReturnId = PurchaseReturn.Id

                             ORDER BY PurchaseReturnLine.[Sequence]

                               FOR JSON PATH
                     ) AS 'purchaseReturnLines'

                FROM dbo.PurchaseReturn

               INNER JOIN dbo.Vendor
                  ON PurchaseReturn.VendorId = Vendor.Id

               INNER JOIN dbo.Clinic
                  ON PurchaseReturn.ClinicId = Clinic.Id

               WHERE PurchaseReturn.Id = Source.Id

                 FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
             ) AS JSON
             ) AS t1