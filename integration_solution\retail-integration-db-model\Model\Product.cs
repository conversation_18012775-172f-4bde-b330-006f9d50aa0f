﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model
{
    public class Product
    {
        [JsonPropertyName("id")]
        public Guid? Id { get; set; }

        [JsonPropertyName("externalSystemCode")]
        public string? ExternalSystemCode { get; set; }

        [JsonPropertyName("code")]
        public string? Code { get; set; }

        [JsonPropertyName("externalCode")]
        public string? ExternalCode { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("pimProductId")]
        public string? PimProductId { get; set; } = string.Empty;

        [JsonPropertyName("category")]
        public ExternalReference? Category { get; set; }

        [JsonPropertyName("subcategory")]
        public ExternalReference? Subcategory { get; set; }

        [JsonPropertyName("vendor")]
        public ExternalReference? Vendor { get; set; }

        [JsonPropertyName("manufacturer")]
        public ExternalReference? Manufacturer { get; set; }

        [JsonPropertyName("color")]
        public ExternalReference? Color { get; set; }

        [Json<PERSON>ropertyName("vendorItemNo")]
        public string? VendorItemNo { get; set; } = string.Empty;

        [JsonPropertyName("gtin")]
        public string? GTIN { get; set; } = string.Empty;

        [JsonPropertyName("imageUrl")]
        public string? ImageUrl { get; set; } = string.Empty;


        public Product() { }
    }
}