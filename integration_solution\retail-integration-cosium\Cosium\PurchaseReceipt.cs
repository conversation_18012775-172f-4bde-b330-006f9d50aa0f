﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Text.Json;
using System.Text;
using System.Text.Json.Nodes;
using WSA.Retail.Integration.Model;

namespace WSA.Retail.Integration.Cosium
{
    public class PurchaseReceipt: Model.PurchaseReceipt
    {
        public PurchaseReceipt() 
        {
            PurchaseReceiptLines = [];
        }

        public PurchaseReceipt(DataRow row)
        {
            string? externalSystemCode = Environment.GetEnvironmentVariable("ExternalSystemCode");
            ArgumentNullException.ThrowIfNull(externalSystemCode);

            string? requestBody = row.IsNull("JSON") ? null : (string?)row["JSON"];
            if (requestBody != null)
            {
                JsonNode? json = JsonNode.Parse(requestBody);
                ArgumentNullException.ThrowIfNull(json);

                try
                {
                    Id = (Guid?)json.AsObject()["id"];
                    ExternalSystemCode = externalSystemCode;
                    DocumentNumber = (string?)json.AsObject()["documentNumber"];
                    ExternalReference = (string?)json.AsObject()["externalReference"];
                    AlternateNumber = (string?)json.AsObject()["alternateNumber"];
                    PurchaseOrder = new()
                    {
                        Id = (Guid?)json.AsObject()["purchaseOrder"]?["id"],
                        DocumentNumber = (string?)json.AsObject()["purchaseOrder"]?["documentNumber"],
                        ExternalReference = (string?)json.AsObject()["purchaseOrder"]?["externalReference"],
                    };
                    Vendor = new()
                    {
                        Id = (Guid?)json.AsObject()["vendor"]?["id"],
                        Code = (string?)json.AsObject()["vendor"]?["code"],
                        ExternalCode = (string?)json.AsObject()["vendor"]?["externalCode"],
                        Name = (string?)json.AsObject()["vendor"]?["name"]
                    };
                    Clinic = new()
                    {
                        Id = (Guid?)json.AsObject()["clinic"]?["id"],
                        Code = (string?)json.AsObject()["clinic"]?["code"],
                        ExternalCode = (string?)json.AsObject()["clinic"]?["externalCode"],
                        Name = (string?)json.AsObject()["clinic"]?["name"]
                    };
                    DocumentDate = DateOnly.FromDateTime((DateTime?)json.AsObject()["documentDate"] ?? DateTime.MinValue);

                    PurchaseReceiptLines = [];
                    JsonArray? jsonArray = json.AsObject()["purchaseReceiptLines"]?.AsArray();
                    if (jsonArray != null)
                    {
                        foreach (var item in jsonArray)
                        {
                            if (item != null)
                            {
                                var thisPurchaseReceiptLine = new Model.PurchaseReceiptLine()
                                {
                                    Id = (Guid?)json.AsObject()["id"],
                                    PurchaseOrderLine = new()
                                    {
                                        Id = (Guid?)item.AsObject()["purchaseOrderLine"]?["id"]
                                    },
                                    Product = new()
                                    {
                                        Id = (Guid?)item.AsObject()["product"]?["id"],
                                        Code = (string?)item.AsObject()["product"]?["code"],
                                        ExternalCode = (string?)item.AsObject()["product"]?["externalCode"],
                                        Name = (string?)item.AsObject()["product"]?["name"]
                                    },
                                    Quantity = (decimal?)item.AsObject()["quantity"],
                                    UnitPrice = (decimal?)item.AsObject()["unitPrice"],
                                    GrossAmount = (decimal?)item.AsObject()["grossAmount"],
                                    DiscountAmount = (decimal?)item.AsObject()["discountAmount"],
                                    AmountExclTax = (decimal?)item.AsObject()["amountExclTax"],
                                    TaxAmount = (decimal?)item.AsObject()["taxAmount"],
                                    AmountInclTax = (decimal?)item.AsObject()["amountInclTax"],
                                    SerialNumber = (string?)item.AsObject()["serialNumber"],
                                };

                                if (int.TryParse((string?)item.AsObject()["sequence"], out var seq))
                                {
                                    thisPurchaseReceiptLine.Sequence = seq;
                                }

                                if (int.TryParse((string?)item.AsObject()["purchaseOrderLine"]?["sequence"], out seq))
                                {
                                    thisPurchaseReceiptLine.PurchaseOrderLine.Sequence = seq;
                                }

                                PurchaseReceiptLines.Add(thisPurchaseReceiptLine);
                            }
                        }
                    }
                }
                catch
                {
                    throw;
                }
            }
        }

        private static HttpClient? _client;

        public static async Task ProcessNewRecordsAsync(ILogger log)
        {
            var scope = InitScope(nameof(ProcessNewRecordsAsync));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(ProcessNewRecordsAsync));

            List<PurchaseReceipt?> receipts = await GetFromDatabase(log);
            log.BeginScope(scope);
            log.LogInformation("Fetched {recordCount} purchase receipts from Cosium database", receipts?.Count);
            if (receipts?.Count > 0)
            {
                await PostListAsync(receipts, log);
            }
            else
            {
                log.BeginScope(scope);
                log.LogInformation("No new purchase receipts are avalable in the Cosium database.");
            }
        }

        private static async Task<List<PurchaseReceipt?>> GetFromDatabase(ILogger log)
        {
            var scope = InitScope(nameof(GetFromDatabase));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(GetFromDatabase));

            List<PurchaseReceipt?> purchaseReceiptList = [];

            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);
            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "SELECT * FROM cosium.PurchaseReceipts WHERE IntegrationRequired = 1";
                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        {
                            if (reader.HasRows)
                            {
                                DataTable dt = new();
                                dt.Load(reader);
                                log.LogInformation("SQL reader returned {rowCount} rows.", dt.Rows.Count);

                                foreach (DataRow dr in dt.Rows)
                                {
                                    PurchaseReceipt newReceipt = new(dr);
                                    purchaseReceiptList.Add(newReceipt);
                                }
                                return purchaseReceiptList;
                            }
                            else
                            {
                                log.LogInformation("No new clinics were retrieved from the database.");
                            }
                        }
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "Attempt to fetch records from the database generated an exception:\r\n{object}", ex.Message);
                }
            }
            return purchaseReceiptList;
        }

        public static async Task<PurchaseReceipt?> GetFromDatabaseById(int thisId, ILogger log)
        {
            var scope = InitScope(nameof(GetFromDatabaseById));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(GetFromDatabaseById));

            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);
            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "SELECT * FROM cosium.PurchaseReceipts WHERE Id = @thisId";
                        cmd.Parameters.AddWithValue("@thisId", thisId);

                        using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                        {
                            if (reader.HasRows)
                            {
                                DataTable dt = new();
                                dt.Load(reader);
                                log.LogInformation("SQL reader has {rowCount} rows.", dt.Rows.Count);

                                DataRow dr = dt.Rows[0];
                                PurchaseReceipt? newRecord = new(dr);

                                return newRecord;
                            }
                            else
                            {
                                log.LogInformation("No new purchase receipts were retrieved from the database.");
                            }
                        }
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    log.LogError(ex, "Attempt to fetch records from the database generated an exception:\r\n{object}", ex.Message);
                }
            }
            return null;
        }

        public static async Task PostListAsync(List<PurchaseReceipt?> receipts, ILogger log)
        {
            var scope = InitScope(nameof(PostListAsync));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(PostListAsync));
            try
            {
                foreach (var receipt in receipts)
                {
                    if (receipt != null)
                    {
                        await receipt.PostAsync(log);
                    }
                }
            }
            catch (Exception ex)
            {
                log.BeginScope(scope);
                log.LogError(ex, "Encountered an exception:\r\n{object}", ex.Message);
            }
        }

        public async Task<PurchaseReceipt?> PostAsync(ILogger log)
        {
            var scope = InitScope(nameof(PostAsync));
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(PostAsync));

            _client ??= Common.GetHttpClient();

            try
            {
                if (this != null)
                {
                    if (this.PurchaseReceiptLines.Count > 0)
                    {
                        await ValidateExternalReferences(log);

                        var requestBody = JsonSerializer.Serialize(this, Common.GetJsonOptions());
                        var content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                        var request = new HttpRequestMessage(HttpMethod.Post, "purchaseReceipts")
                        {
                            Content = content
                        };

                        log.LogInformation("Sending purchaseReceipt: {code} to API\r\n{requestBody}", this.DocumentNumber, requestBody);
                        HttpResponseMessage response = await _client.SendAsync(request);
                        var responseBody = await response.Content.ReadAsStringAsync();
                        responseBody = JsonObject.Parse(responseBody)?.ToJsonString(Common.GetJsonOptions()) ?? "";
                        log.LogInformation("API response:\r\n{status}\r\n{responseBody}",
                            response.StatusCode.ToString() + " - " + response.RequestMessage, responseBody);

                        if (response.StatusCode == System.Net.HttpStatusCode.OK)
                        {
                            log.LogInformation("Successfully updated purchaseReceipt: {code}", this.DocumentNumber);
                            UpdateIsIntegrated();
                            return JsonSerializer.Deserialize<PurchaseReceipt?>(responseBody, Common.GetJsonOptions());
                        }
                        else
                        {
                            log.LogError("Attempt to update purchaseReceipt: {code} failed\r\nMessage:{responseBody}",
                                this.DocumentNumber, responseBody);
                        }
                    }
                    else
                    {
                        UpdateIsIntegrated();
                    }
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Encountered an exception:\r\n{object}", ex.Message);
            }
            return null;
        }

        public static async Task<PurchaseReceipt?> GetAsync(string? code = null, string? externalCode = null)
        {
            HttpRequestMessage request;
            _client ??= Common.GetHttpClient();
            if (code != null)
            {
                request = new HttpRequestMessage(HttpMethod.Get, "purchaseReceipts?code=" + code);
            }
            else
            {
                ArgumentNullException.ThrowIfNull(externalCode);
                request = new HttpRequestMessage(HttpMethod.Get, "purchaseReceipts?externalCode=" + externalCode);
            }

            var response = await _client.SendAsync(request);
            if (response != null)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                switch (response.StatusCode)
                {
                    case System.Net.HttpStatusCode.OK:

                        if (responseBody != null)
                        {
                            var records = JsonSerializer.Deserialize<List<PurchaseReceipt?>?>(responseBody, Common.GetJsonOptions());
                            if (records != null)
                            {
                                return records.FirstOrDefault();
                            }
                        }
                        break;

                    case System.Net.HttpStatusCode.NoContent:
                        return null;

                    default: return null;
                }
            }
            return null;
        }

        private bool UpdateIsIntegrated()
        {
            string? connString = Environment.GetEnvironmentVariable("SqlConnectionString");
            ArgumentNullException.ThrowIfNull(connString);
            ArgumentNullException.ThrowIfNull(this.ExternalReference);

            using SqlConnection conn = new(connString);
            {
                try
                {
                    conn.Open();
                    using SqlCommand cmd = conn.CreateCommand();
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.CommandText = "UPDATE cosium.PurchaseReceipts SET IntegrationRequired = 0, IntegrationDate = sysutcdatetime() WHERE Id = @id";
                        cmd.Parameters.AddWithValue("@id", this.ExternalReference);
                        cmd.ExecuteNonQuery();
                        conn.Close();
                        return true;
                    }
                }
                catch
                {
                    return false;
                }
            }
        }

        private static Dictionary<string, object> InitScope(string procedureName)
        {
            return new Dictionary<string, object>() {
                { "appName", "Cosium" },
                { "className", nameof(PurchaseReceipt) },
                { "procedureName", procedureName }
            };
        }

        public async Task ValidateExternalReferences(ILogger log)
        {
            await ValidatePurchaseOrder(log);
            await Cosium.Vendor.ValidateExternalReference(Vendor, log);
            await Cosium.Clinic.ValidateExternalReference(Clinic);
            await ValidateProducts(log);
        }

        public async Task ValidatePurchaseOrder(ILogger log)
        {
            var scope = InitScope(nameof(ValidatePurchaseOrder));
            AddEntityToScope(ref scope, this);
            log.BeginScope(scope);
            log.LogInformation("{procedureName} started", nameof(ValidatePurchaseOrder));

            if (PurchaseOrder != null && PurchaseOrder.ExternalReference != null)
            {
                ArgumentNullException.ThrowIfNull(PurchaseOrder.ExternalReference);
                Cosium.PurchaseOrder? record = await Cosium.PurchaseOrder.GetAsync(externalCode: PurchaseOrder.ExternalReference);
                if (record == null)
                {
                    log.LogDebug("Purchase Order: {code} does not exist in the integration", PurchaseOrder.ExternalReference);
                    record = await Cosium.PurchaseOrder.GetFromDatabaseById(int.Parse(PurchaseOrder.ExternalReference), log);
                    if (record != null)
                    {
                        record = await record.PostAsync(log);
                    }
                }

                ArgumentNullException.ThrowIfNull(record);
                PurchaseOrder.Id = record.Id;
                PurchaseOrder.DocumentNumber = record.DocumentNumber;
                PurchaseOrder.ExternalReference = record.ExternalReference;
            }
        }

        public async Task ValidateProducts(ILogger log)
        {
            if (PurchaseReceiptLines != null)
            {
                Dictionary<string, Model.ExternalReference> products = [];
                foreach (var line in PurchaseReceiptLines)
                {
                    if (line?.Product?.ExternalCode != null)
                    {
                        if (!products.ContainsKey(line.Product.ExternalCode))
                        {
                            products.Add(line.Product.ExternalCode, line.Product);
                        }
                    }
                }

                foreach (var product in products.Values)
                {
                    await Cosium.Product.ValidateExternalReference(product, log);
                }
            }
        }

        private static void AddEntityToScope(ref Dictionary<string, object> scope, PurchaseReceipt purchaseReceipt)
        {
            scope["entityName"] = "PurchaseReceipt";
            scope["entityCode"] = purchaseReceipt.DocumentNumber ?? "";
        }
    }
}
