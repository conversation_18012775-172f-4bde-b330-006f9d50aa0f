﻿SET XACT_ABORT ON;
BEGIN TRANSACTION;
IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'PIM')
BEGIN
    CREATE USER [PIM] WITH PASSWORD = 'WS@R3ta!l'
    IF NOT EXISTS (SELECT 1 FROM sys.database_role_members rm
                   JOIN sys.database_principals r ON rm.role_principal_id = r.principal_id
                   JOIN sys.database_principals m ON rm.member_principal_id = m.principal_id
                   WHERE r.name = 'db_owner' AND m.name = 'PIM')
    BEGIN
        ALTER ROLE db_owner ADD MEMBER [PIM];
    <PERSON><PERSON>

END
COMMIT TRANSACTION;