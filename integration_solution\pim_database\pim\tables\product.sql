﻿CREATE TABLE [pim].[Product]
(
    [Id]                    UNIQUEIDENTIFIER CONSTRAINT [DF_Product_Id] DEFAULT NEWID() NOT NULL,
    [Name]                  NVARCHAR(100) NOT NULL,
    [ColorId]               UNIQUEIDENTIFIER NULL,
    [IsAvailable]           BIT NULL,
    [IsPhasedOut]           BIT NULL,
    [ListPrice]             DECIMAL(18, 4) NULL,
    [ProductSource]         NVARCHAR(20) NULL,
    [ProductType]           NVARCHAR(20) NULL,
    [Ranking]               INTEGER NULL,
    [ReleaseDate]           DATETIME2 NULL,
    [Sku]                   NVARCHAR(50) NULL,
    [State]                 NVARCHAR(20) NULL,
    [CreatedAt]             DATETIME2 NULL,
    [UpdatedAt]             DATETIME2 NULL,
    [SystemCreatedOn]       DATETIME2 CONSTRAINT [DF_Product_SystemCreatedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    [SystemModifiedOn]      DATETIME2 CONSTRAINT [DF_Product_SystemModifiedOn] DEFAULT (sysutcdatetime()) NOT NULL,
    IntegrationRequired     BIT NOT NULL CONSTRAINT [DF_Product_IntegrationRequired] DEFAULT 1,
    [IntegratedOn]          DATETIME2(7) NULL
    CONSTRAINT              [PK_Product_Id] PRIMARY KEY CLUSTERED ([Id] ASC)
)
GO

CREATE INDEX IX_Product_Sku
ON [pim].[Product] ([Sku])
GO

CREATE INDEX IX_Product_SystemModifiedOn
ON [pim].[Product] ([SystemModifiedOn])
GO

CREATE INDEX IX_Product_IntegrationRequired
ON [pim].[Product] ([IntegrationRequired], [Id])
GO
