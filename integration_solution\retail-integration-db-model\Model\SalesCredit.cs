﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model;

public class SalesCredit
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("externalSystemCode")]
    public string? ExternalSystemCode { get; set; }

    [JsonPropertyName("documentNumber")]
    public string? DocumentNumber { get; set; }

    [JsonPropertyName("externalReference")]
    public string? ExternalReference { get; set; }

    [JsonPropertyName("alternateNumber")]
    public string? AlternateNumber { get; set; }

    [JsonPropertyName("patient")]
    public ExternalReference? Patient { get; set; }

    [JsonPropertyName("clinic")]
    public ExternalReference? Clinic { get; set; }

    [JsonPropertyName("documentDate")]
    public DateOnly? DocumentDate { get; set; }

    [JsonPropertyName("salesOrder")]
    public ExternalDocumentReference? SalesOrder { get; set; }

    [JsonPropertyName("salesCreditLines")]
    public List<SalesCreditLine?> SalesCreditLines { get; set; }


    public SalesCredit() 
    {
        SalesCreditLines = [];
    }
}
