{"name": "Manage-BC Data Transfer Documentation", "description": "Comprehensive documentation of entities and their properties involved in data transfer between Manage and Business Central (BC) systems", "version": "1.0.0", "sources": [{"name": "integration_solution", "path": "../integration_solution", "include": ["**/*.cs", "**/*.md"], "exclude": ["**/bin/**", "**/obj/**", "**/packages/**", "**/.vs/**", "**/node_modules/**", "**/TestResults/**", "**/.git/**"]}], "output": {"format": "markdown", "destination": "./generated"}, "templates": {"entity": {"pattern": "**/*Entity.cs", "template": "templates/entity_template.md", "metadata": {"extract": ["properties", "constraints", "relationships", "indexes", "validation_attributes"]}}, "adapter": {"pattern": "**/*Adapter.cs", "template": "templates/adapter_template.md", "metadata": {"extract": ["methods", "transformations", "validation_rules", "business_logic"]}}, "configuration": {"pattern": "**/*Configuration.cs", "template": "templates/configuration_template.md", "metadata": {"extract": ["table_mappings", "column_constraints", "indexes", "foreign_keys", "default_values"]}}, "domain_model": {"pattern": "**/Models/**/*.cs", "exclude": ["**/*Entity.cs", "**/*Configuration.cs", "**/*Adapter.cs"], "template": "templates/domain_model_template.md", "metadata": {"extract": ["properties", "json_attributes", "validation_rules", "business_rules"]}}}, "watch": {"enabled": true, "patterns": ["**/*.cs"], "debounce": 2000}, "metadata": {"author": "ERP Integration Team", "created": "2025-01-15", "updated": "2025-07-15", "tags": ["ERP", "Integration", "Manage", "Business Central", "Data Transfer", "Entity Framework", "Constraints"], "version_control": {"track_changes": true, "include_commit_info": true}}, "generation": {"include_toc": true, "include_diagrams": true, "cross_reference": true, "constraint_analysis": true}}