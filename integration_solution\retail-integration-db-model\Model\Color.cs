﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Model
{
    public class Color
    {
        [JsonPropertyName("id")]
        public Guid? Id { get; set; }
        [JsonPropertyName("externalSystemCode")]
        public string? ExternalSystemCode { get; set; }
        [JsonPropertyName("code")]
        public string? Code { get; set; }
        [JsonPropertyName("externalCode")]
        public string? ExternalCode { get; set; }
        [JsonPropertyName("name")]
        public string? Name { get; set; }
        [JsonPropertyName("hexCode")]
        public string? HexCode { get; set; }


        public Color() { }
    }
}
