﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|AnyCPU">
      <Configuration>Debug</Configuration>
      <Platform>AnyCPU</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|AnyCPU">
      <Configuration>Release</Configuration>
      <Platform>AnyCPU</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>ba72576c-2778-45e2-a07f-8c7711465c12</ProjectGuid>
  </PropertyGroup>
  <PropertyGroup>
    <PrepareForBuildDependsOn>
    </PrepareForBuildDependsOn>
  </PropertyGroup>
  <Import Condition=" Exists('Deployment.targets') " Project="Deployment.targets" />
  <Import Project="$(MSBuildToolsPath)\Microsoft.Common.targets" />
  <!-- vertag<:>start tokens<:>maj.min -->
  <Import Condition=" Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Deployment\1.1\DeploymentProject.targets') " Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Deployment\1.1\DeploymentProject.targets" />
  <!-- vertag<:>end -->
  <ItemGroup>
    <Content Include="azuredeploy.json" />
    <Content Include="azuredeploy.parameters.json" />
    <None Include="Deployment.targets">
      <Visible>False</Visible>
    </None>
    <Content Include="Deploy-AzureResourceGroup.ps1" />
  </ItemGroup>
  <Target Name="GetReferenceAssemblyPaths" />
</Project>