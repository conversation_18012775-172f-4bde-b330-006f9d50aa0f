﻿using Microsoft.Identity.Client;

namespace WSA.Retail.Integration.PIM
{
    public class Product
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public Guid? ColorId { get; set; }
        public bool? IsAvailable { get; set; }
        public bool? IsPhasedOut { get; set; }
        public double? ListPrice { get; set; }
        public string? ProductSource { get; set; }
        public string? ProductType { get; set;}
        public int? Ranking { get; set; }
        public DateTime? ReleaseDate { get; set; }
        public string? Sku { get; set; }
        public string? State { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
