# Entity Configurations and Database Constraints

## 📋 Overview

Entity configurations define the database schema, constraints, indexes, and relationships for all entities in the Manage-BC integration system. These configurations ensure data integrity and optimal performance.

## 🔧 Configuration Extensions

### ManageEntityConfigurationExtensions
**Source**: `integration_solution/retail-integration-manage/Manage/Core/ManageEntityConfigurationExtensions.cs`

Provides common configuration methods for Manage system entities.

#### Key Methods

##### ConfigureManageIdField
```csharp
public static EntityTypeBuilder<T> ConfigureManageIdField<T>(this EntityTypeBuilder<T> builder)
    where T : class, IIdentifiable
```
- **Column**: `Id`
- **Type**: `uniqueidentifier`
- **Required**: Yes
- **Purpose**: Primary key configuration

##### ConfigureManageEventMetadataFields
```csharp
public static EntityTypeBuilder<T> ConfigureManageEventMetadataFields<T>(this EntityTypeBuilder<T> builder)
    where T : class, IEventMetadata
```
- **EventType**: `nvarchar(50)`, Required
- **MessageId**: `uniqueidentifier`, Required
- **Timestamp**: `datetime2(7)`, Required
- **LocalTimestamp**: `datetime2(7)`, Required

##### ConfigureManageEventProcessingInfoFields
```csharp
public static EntityTypeBuilder<T> ConfigureManageEventProcessingInfoFields<T>(this EntityTypeBuilder<T> builder)
    where T : class, IEventProcessingInfo
```
- **BlobPath**: `nvarchar(255)`, Optional
- **Status**: `int`, Required (enum conversion)
- **ProcessedOn**: `datetime2(7)`, Optional

## 🏢 Master Data Configurations

### CategoryMappingConfiguration
**Source**: `integration_solution/retail-integration-manage/Manage/Models/CategoryMappings/CategoryMappingConfiguration.cs`

Configures category mapping entities for hearing aid business logic.

#### Configuration Details
```csharp
public void Configure(EntityTypeBuilder<CategoryMappingEntity> builder)
{
    builder.ToTable("CategoryMapping", "manage");
    builder.HasKey(e => e.Id);
    
    // Primary Key
    builder.Property(e => e.Id)
        .HasDefaultValueSql("NEWID()")
        .ValueGeneratedOnAdd();
    
    // Business Logic Flag
    builder.Property(e => e.IsHearingAid)
        .HasDefaultValue(false)
        .IsRequired();
    
    // Audit Fields
    builder.Property(e => e.CreatedOn)
        .HasDefaultValueSql("sysutcdatetime()")
        .ValueGeneratedOnAdd()
        .IsRequired();
    
    builder.Property(e => e.ModifiedOn)
        .HasDefaultValueSql("sysutcdatetime()")
        .ValueGeneratedOnAdd()
        .IsRequired();
    
    // Unique Index
    builder.HasIndex(e => e.CategoryId)
        .IsUnique()
        .HasDatabaseName("IX_CategoryMapping_CategoryId");
}
```

#### Constraints
- **Primary Key**: `Id` (auto-generated GUID)
- **Unique Index**: `CategoryId` must be unique
- **Default Values**: `IsHearingAid = false`, timestamps auto-generated
- **Schema**: `manage`

### InventoryCountryConfiguration
**Source**: `integration_solution/retail-integration-manage/Manage/Models/Countries/InventoryCountryConfiguration.cs`

Configures country lookup entities for inventory management.

#### Configuration Details
```csharp
public void Configure(EntityTypeBuilder<InventoryCountryEntity> builder)
{
    builder.ToTable("InventoryCountry", "manage");
    builder.HasKey(e => e.Id).HasName("PK_InventoryCountry_Id");
    
    // Primary Key
    builder.Property(e => e.Id)
        .HasDefaultValueSql("NEWID()");
    
    // Business Fields
    builder.Property(e => e.Name)
        .IsRequired()
        .HasMaxLength(100);
    
    builder.Property(e => e.Code)
        .IsRequired()
        .HasMaxLength(20);
    
    // Audit Fields
    builder.Property(e => e.CreatedOn)
        .HasDefaultValueSql("sysutcdatetime()");
    
    builder.Property(e => e.ModifiedOn)
        .HasDefaultValueSql("sysutcdatetime()");
    
    // Unique Index
    builder.HasIndex(e => e.Name)
        .IsUnique()
        .HasDatabaseName("IX_InventoryCountry_Name");
}
```

#### Constraints
- **Primary Key**: `Id` (auto-generated GUID)
- **Required Fields**: `Name` (max 100), `Code` (max 20)
- **Unique Index**: `Name` must be unique
- **Schema**: `manage`

### ClinicEntityConfiguration
**Source**: `integration_solution/wsa-retail-integration-model/Models/Clinics/ClinicEntityConfiguration.cs`

Configures clinic entities with address and contact information.

#### Configuration Details
```csharp
public void Configure(EntityTypeBuilder<ClinicEntity> builder)
{
    builder.ToTable("Clinic");
    builder.HasKey(x => x.Id);
    
    // Common Configurations
    builder.ConfigureMasterDataFields();
    builder.ConfigureAddressFields();
    builder.ConfigureContactFields();
    builder.ConfigureAuditInfoFields();
    
    // Company Relationship
    builder.Property(x => x.CompanyId)
        .HasColumnName("CompanyId")
        .HasColumnType("uniqueidentifier")
        .IsRequired(false);
    
    builder.HasOne(x => x.CompanyEntity)
        .WithMany()
        .HasForeignKey(x => x.CompanyId);
}
```

#### Constraints
- **Primary Key**: `Id`
- **Foreign Key**: `CompanyId` → `Company.Id` (optional)
- **Inherited Constraints**: Master data, address, contact, and audit fields
- **Schema**: `dbo` (default)

## 📄 Transaction Configurations

### OrderEventConfiguration
**Source**: `integration_solution/retail-integration-manage/Manage/Models/PurchaseOrders/OrderEventConfiguration.cs`

Configures purchase order event processing entities.

#### Configuration Details
```csharp
public void Configure(EntityTypeBuilder<OrderEventEntity> builder)
{
    builder.ToTable("OrderEvent");
    builder.HasKey(x => x.Id);
    
    // Common Manage Configurations
    builder.ConfigureManageIdField();
    builder.ConfigureManageEventMetadataFields();
    builder.ConfigureManageOrderIdentifersFields();
    builder.ConfigureManageLocationScopedField();
    builder.ConfigureManageSupplierScopedField();
    builder.ConfigureManageEventProcessingInfoFields();
    builder.ConfigureAuditInfoFields();
}
```

#### Constraints
- **Primary Key**: `Id`
- **Required Fields**: Event metadata, order identifiers
- **Optional Fields**: Location and supplier scoping
- **Event Processing**: Status tracking and blob path storage

### InvoiceEventConfiguration
**Source**: `integration_solution/retail-integration-manage/Manage/Models/SalesInvoices/InvoiceEventConfiguration.cs`

Configures sales invoice event processing entities.

#### Configuration Details
```csharp
public void Configure(EntityTypeBuilder<InvoiceEventEntity> builder)
{
    builder.ToTable("InvoiceEvent");
    builder.HasKey(x => x.Id);
    
    // Common Manage Configurations
    builder.ConfigureManageIdField();
    builder.ConfigureManageEventMetadataFields();
    builder.ConfigureManageInvoiceIdentifersFields();
    builder.ConfigureManageLocationScopedField();
    builder.ConfigureManagePatientScopedField();
    builder.ConfigureManageFunderScopedField();
    builder.ConfigureManageEventProcessingInfoFields();
    builder.ConfigureAuditInfoFields();
}
```

#### Constraints
- **Primary Key**: `Id`
- **Required Fields**: Event metadata, invoice identifiers
- **Optional Fields**: Location, patient, and funder scoping
- **Event Processing**: Status tracking and blob path storage

### OrderLineItemAcceptedEventConfiguration
**Source**: `integration_solution/retail-integration-manage/Manage/Models/PurchaseReceipts/OrderLineItemAcceptedEventConfiguration.cs`

Configures purchase receipt line item events.

#### Configuration Details
```csharp
public void Configure(EntityTypeBuilder<OrderLineItemAcceptedEventEntity> builder)
{
    builder.ToTable("OrderLineItemAcceptedEvent");
    builder.HasKey(x => x.Id);
    
    // Common Configurations
    builder.ConfigureManageIdField();
    builder.ConfigureManageEventMetadataFields();
    builder.ConfigureManageOrderIdentifersFields();
    builder.ConfigureManageEventProcessingInfoFields();
    builder.ConfigureAuditInfoFields();
    
    // Line Item Specific
    builder.Property(x => x.LineItemId)
        .HasColumnName("LineItemId")
        .HasColumnType("UNIQUEIDENTIFIER")
        .IsRequired(true);
}
```

#### Constraints
- **Primary Key**: `Id`
- **Required Fields**: `LineItemId`, event metadata, order identifiers
- **Event Processing**: Status tracking and blob path storage

## 🔍 Common Configuration Patterns

### Master Data Fields
Applied via `ConfigureMasterDataFields()`:
- **Id**: `uniqueidentifier`, Primary Key
- **Code**: `nvarchar(50)`, Required, Unique
- **Name**: `nvarchar(255)`, Optional
- **AlternateCode**: `nvarchar(50)`, Optional

### Address Fields
Applied via `ConfigureAddressFields()`:
- **Address**: `nvarchar(100)`, Optional
- **Address2**: `nvarchar(100)`, Optional
- **City**: `nvarchar(50)`, Optional
- **Region**: `nvarchar(50)`, Optional
- **Country**: `nvarchar(50)`, Optional
- **PostalCode**: `nvarchar(20)`, Optional

### Contact Fields
Applied via `ConfigureContactFields()`:
- **Phone**: `nvarchar(20)`, Optional
- **Email**: `nvarchar(100)`, Optional

### Audit Info Fields
Applied via `ConfigureAuditInfoFields()`:
- **CreatedOn**: `datetime2(7)`, Required, Default: `sysutcdatetime()`
- **ModifiedOn**: `datetime2(7)`, Required, Auto-updated

## 📊 Index Strategies

### Unique Indexes
- **CategoryMapping**: `IX_CategoryMapping_CategoryId` on `CategoryId`
- **InventoryCountry**: `IX_InventoryCountry_Name` on `Name`
- **Master Data**: Unique indexes on `Code` fields

### Performance Indexes
- **Foreign Keys**: Automatic indexes on all foreign key relationships
- **Event Processing**: Indexes on `Status` and `ProcessedOn` for event queries
- **Document Numbers**: Indexes on `DocumentNumber` fields for transaction lookups

## 🔒 Data Integrity Rules

### Referential Integrity
- All foreign key relationships enforce referential integrity
- Cascade delete rules defined where appropriate
- Optional relationships allow null values

### Check Constraints
- Positive values enforced for quantities and amounts
- Date constraints ensure logical date relationships
- Status values restricted to defined enumerations

### Default Values
- GUIDs auto-generated for primary keys
- Timestamps auto-generated and updated
- Boolean flags have appropriate defaults

---

*These configurations ensure data consistency and optimal performance across the Manage-BC integration system.*
