﻿using System.Text.Json.Serialization;

namespace WSA.Retail.Integration.Base
{
    public class Event
    {
        [JsonPropertyName("id")]
        public required string Id { get; set; }

        [JsonPropertyName("source")]
        public required string Source { get; set; }

        [JsonPropertyName("specversion")]
        public required string SpecVersion { get; set; }

        [JsonPropertyName("type")]
        public required string EventType { get; set; }

        [JsonPropertyName("subject")]
        public string? Subject { get; set; }

        [JsonPropertyName("time")]
        public string? EventTime { get; set; }

        [JsonPropertyName("data")]
        public required object Data { get; set; }
    }
}
