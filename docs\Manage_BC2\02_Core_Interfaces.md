# Core Interfaces and Base Classes

## 🏗️ Interface Hierarchy

The integration system is built on a foundation of well-defined interfaces that provide consistent structure and behavior across all entities.

## 📋 Base Interfaces

### IIdentifiable
**Source**: `integration_solution/wsa-retail-integration-model/Core/IIdentifiable.cs`

Provides unique identification for all entities.

```csharp
public interface IIdentifiable
{
    Guid Id { get; set; }
}
```

**Properties:**
- `Id` (Guid): Unique identifier for the entity
  - **Required**: Yes
  - **Default**: `Guid.Empty`
  - **Constraints**: Must be a valid GUID

### ICodeIdentifiable
**Source**: `integration_solution/wsa-retail-integration-model/Core/ICodeIdentifiable.cs`

Provides business code identification for entities.

```csharp
public interface ICodeIdentifiable
{
    string Code { get; set; }
    string? AlternateCode { get; set; }
}
```

**Properties:**
- `Code` (string): Business identifier code
  - **Required**: Yes
  - **Max Length**: Varies by entity (typically 20-50 characters)
  - **Constraints**: Must be unique within entity type
- `AlternateCode` (string?): Alternative business code
  - **Required**: No
  - **Max Length**: Varies by entity

### INameable
**Source**: `integration_solution/wsa-retail-integration-model/Core/INameable.cs`

Provides naming capabilities for entities.

```csharp
public interface INameable
{
    string? Name { get; set; }
}
```

**Properties:**
- `Name` (string?): Display name for the entity
  - **Required**: No (but recommended for most entities)
  - **Max Length**: Varies by entity (typically 100-255 characters)

### IAuditInfo
**Source**: `integration_solution/wsa-retail-integration-model/Core/IAuditInfo.cs`

Provides audit trail information for all entities.

```csharp
public interface IAuditInfo
{
    DateTime CreatedOn { get; set; }
    DateTime ModifiedOn { get; set; }
}
```

**Properties:**
- `CreatedOn` (DateTime): Entity creation timestamp
  - **Required**: Yes
  - **Default**: `sysutcdatetime()` (SQL Server function)
  - **Constraints**: UTC timestamp
- `ModifiedOn` (DateTime): Last modification timestamp
  - **Required**: Yes
  - **Default**: `sysutcdatetime()` (SQL Server function)
  - **Constraints**: UTC timestamp, auto-updated on changes

## 🎯 Master Data Interfaces

### IMasterData
**Source**: `integration_solution/wsa-retail-integration-model/Core/IMasterData.cs`

Core interface for all master data entities (Products, Clinics, Patients, etc.).

```csharp
public interface IMasterData :
    IIdentifiable,
    ICodeIdentifiable,
    INameable,
    IExternallyMapable,
    ITelemetryEntity
{
    public bool HasChanges(IMasterData? oldEntity);
    public bool MasterDataHasChanges(IMasterData? oldEntity);
}
```

**Inherited Properties:**
- From `IIdentifiable`: `Id`
- From `ICodeIdentifiable`: `Code`, `AlternateCode`
- From `INameable`: `Name`
- From `IExternallyMapable`: `ExternalSystemCode`, `ExternalCode`
- From `ITelemetryEntity`: Logging capabilities

**Methods:**
- `HasChanges()`: Determines if entity has changes compared to previous version
- `MasterDataHasChanges()`: Base implementation for change detection

### IMasterDataEntity
**Source**: `integration_solution/wsa-retail-integration-model/Core/IMasterDataEntity.cs`

Database entity interface for master data.

```csharp
public interface IMasterDataEntity : 
    IIdentifiable,
    ICodeIdentifiable,
    INameable,
    ILoggingEnabled
{
}
```

## 🏢 Address and Contact Interfaces

### IAddress
**Source**: `integration_solution/wsa-retail-integration-model/Core/IAddress.cs`

Provides address information for entities like Clinics, Patients, Companies.

```csharp
public interface IAddress
{
    string? Address { get; set; }
    string? Address2 { get; set; }
    string? City { get; set; }
    string? Region { get; set; }
    string? Country { get; set; }
    string? PostalCode { get; set; }
}
```

**Properties:**
- `Address` (string?): Primary address line
  - **Max Length**: Typically 100 characters
- `Address2` (string?): Secondary address line
  - **Max Length**: Typically 100 characters
- `City` (string?): City name
  - **Max Length**: Typically 50 characters
- `Region` (string?): State/Province/Region
  - **Max Length**: Typically 50 characters
- `Country` (string?): Country name or code
  - **Max Length**: Typically 50 characters
  - **Constraints**: May be validated against country lookup
- `PostalCode` (string?): ZIP/Postal code
  - **Max Length**: Typically 20 characters

### IContact
**Source**: `integration_solution/wsa-retail-integration-model/Core/IContact.cs`

Provides contact information for entities.

```csharp
public interface IContact
{
    string? Phone { get; set; }
    string? Email { get; set; }
}
```

**Properties:**
- `Phone` (string?): Phone number
  - **Max Length**: Typically 20 characters
  - **Format**: Various international formats supported
- `Email` (string?): Email address
  - **Max Length**: Typically 100 characters
  - **Validation**: Must be valid email format when provided

## 💰 Financial Interfaces

### IPriced
**Source**: `integration_solution/wsa-retail-integration-model/Core/IPriced.cs`

Provides pricing information for entities.

```csharp
public interface IPriced
{
    decimal? UnitPrice { get; set; }
    decimal? TotalAmount { get; set; }
}
```

**Properties:**
- `UnitPrice` (decimal?): Price per unit
  - **Precision**: Typically 18,4 (18 digits, 4 decimal places)
  - **Currency**: Based on system configuration
- `TotalAmount` (decimal?): Total amount
  - **Precision**: Typically 18,4 (18 digits, 4 decimal places)

### IQuantifiable
**Source**: `integration_solution/wsa-retail-integration-model/Core/IQuantifiable.cs`

Provides quantity information for transaction line items.

```csharp
public interface IQuantifiable
{
    decimal? Quantity { get; set; }
}
```

**Properties:**
- `Quantity` (decimal?): Quantity amount
  - **Precision**: Typically 18,4 (18 digits, 4 decimal places)
  - **Constraints**: Usually positive values

## 🔄 Integration Interfaces

### IExternallyMapable
**Source**: `integration_solution/wsa-retail-integration-model/Core/IExternallyMapable.cs`

Provides external system mapping capabilities.

```csharp
public interface IExternallyMapable
{
    string? ExternalSystemCode { get; set; }
    string? ExternalCode { get; set; }
}
```

**Properties:**
- `ExternalSystemCode` (string?): Code identifying the external system
  - **Examples**: "MANAGE", "BC", "SIMPLY", "SYCLE"
  - **Max Length**: Typically 20 characters
- `ExternalCode` (string?): Entity identifier in external system
  - **Max Length**: Typically 50 characters
  - **Purpose**: Maps to external system's primary key or business code

## 📄 Document Interfaces

### IDocument
**Source**: `integration_solution/wsa-retail-integration-model/Core/IDocument.cs`

Base interface for transaction documents (Orders, Invoices, etc.).

```csharp
public interface IDocument : IIdentifiable, IDocumentNumbered, IDocumentDated
{
}
```

### IDocumentNumbered
**Source**: `integration_solution/wsa-retail-integration-model/Core/IDocumentNumbered.cs`

Provides document numbering.

```csharp
public interface IDocumentNumbered
{
    string? DocumentNumber { get; set; }
}
```

### IDocumentDated
**Source**: `integration_solution/wsa-retail-integration-model/Core/IDocumentDated.cs`

Provides document dating.

```csharp
public interface IDocumentDated
{
    DateOnly? DocumentDate { get; set; }
}
```

## 🔍 Association Interfaces

### IClinicAssociated
**Source**: `integration_solution/wsa-retail-integration-model/Core/IClinicAssociated.cs`

Associates entities with clinics.

```csharp
public interface IClinicAssociated
{
    Guid? ClinicId { get; set; }
}
```

### IPatientAssociated
**Source**: `integration_solution/wsa-retail-integration-model/Core/IPatientAssociated.cs`

Associates entities with patients.

```csharp
public interface IPatientAssociated
{
    Guid? PatientId { get; set; }
}
```

### IProductAssociated
**Source**: `integration_solution/wsa-retail-integration-model/Core/IProductAssociated.cs`

Associates entities with products.

```csharp
public interface IProductAssociated
{
    Guid? ProductId { get; set; }
}
```

---

*These core interfaces provide the foundation for all entity definitions and ensure consistent behavior across the integration system.*
